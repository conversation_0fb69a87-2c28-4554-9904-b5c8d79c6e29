image: node:22-slim

stages:
  - build
  - deploy

build_and_publish:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - export SENTRY_CLI_DISABLE_TLS_VERIFY=1
    - echo "NODE_ENV=${NODE_ENV}" > .env.production
    - echo "APP_URL=${APP_URL}" >> .env.production
    - echo "NUXT_PUBLIC_SANCTUM_BASE_URL=${NUXT_PUBLIC_SANCTUM_BASE_URL}" >> .env.production
    - echo "NUXT_PUBLIC_ECHO_KEY=${NUXT_PUBLIC_ECHO_KEY}" >> .env.production
    - echo "NUXT_PUBLIC_ECHO_AUTHENTICATION_BASE_URL=${NUXT_PUBLIC_ECHO_AUTHENTICATION_BASE_URL}" >> .env.production
    - echo "NUXT_PUBLIC_ECHO_SCHEME=${NUXT_PUBLIC_ECHO_SCHEME}" >> .env.production
    - echo "NUXT_PUBLIC_ECHO_HOST=${NUXT_PUBLIC_ECHO_HOST}" >> .env.production
    - echo "NUXT_PUBLIC_ECHO_PORT=${NUXT_PUBLIC_ECHO_PORT}" >> .env.production
    - echo "PORT=${PORT}" >> .env.production
    - echo "SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}" >> .env.production
  script:
    - docker build -t "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA" .
    - docker push "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA"
  artifacts:
    name: "build-env-$CI_COMMIT_SHORT_SHA"
    expire_in: 1 week
    paths:
      - .env.production
  only:
    - main

deploy_production:
  stage: deploy
  tags:
    - shell
  before_script:
    - echo "$CI_JOB_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
  dependencies:
    - build_and_publish
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    - docker compose down || true
    - docker compose pull
    - TAG=$CI_COMMIT_SHORT_SHA docker compose up -d
  environment:
    name: production
    url: https://gunpost.ru
  only:
    - main
