#!/usr/bin/env bash
set -euo pipefail

# 1. Скачиваем пакет
PKG_FILE=$(npm pack @nuxt/ui-pro)
echo "PKG_FILE: $PKG_FILE"

# # 2. Распаковываем архив
gtar -xzf "$PKG_FILE"

# # 3. Находим нужный файл
TARGET_FILE=$(find package/dist/shared/ -name 'ui-pro.*.cjs' | head -n 1)
echo "TARGET_FILE: $TARGET_FILE"

if [[ ! -f "$TARGET_FILE" ]]; then
  echo "Файл не найден: $TARGET_FILE" >&2
  exit 1
fi

# 4. Патчим функцию validateLicense
for FILE in package/dist/shared/ui-pro.*.{cjs,mjs}; do
  if [[ -f "$FILE" ]]; then
    echo "Патчим файл: $FILE"
    sed -i '' 's|if (!opts.key)|return true; if (!opts.key)|' "$FILE"
  fi
done

echo "Патч успешно применён к $TARGET_FILE"

# 5. Упаковываем обратно
gtar -czf $PKG_FILE package

# 6. Удаляем временную папку
# rm -rf package
echo "Готово! Новый архив: $PKG_FILE"

# 7. Добавляем в проект
pnpm add ./$PKG_FILE
pnpm i