services:
  app:
    build: .
    restart: always
    networks:
      - traefik
    env_file:
      - .env
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.gunpost-nuxt.rule=Host(`gunpost.ru`)"
      - "traefik.http.routers.gunpost-nuxt.entrypoints=websecure"
      - "traefik.http.routers.gunpost-nuxt.tls.certresolver=myresolver"
      - "traefik.http.routers.gunpost-nuxt.service=gunpost-nuxt"
      - "traefik.http.services.gunpost-nuxt.loadbalancer.server.port=3000"
networks:
  traefik:
    external: true
