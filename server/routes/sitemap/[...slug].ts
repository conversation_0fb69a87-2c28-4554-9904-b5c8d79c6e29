export default defineEventHandler(async (event) => {
  const xmlData = await $fetch(`https://cdn.gunpost.ru${event.path}`, {
    responseType: "text"
  });

  if (!xmlData) {
    return;
  }

  if (event.path.endsWith(".xml.gz")) {
    event.node.res.setHeader("Content-Type", "application/gzip");
  } else if (event.path.endsWith(".xml")) {
    event.node.res.setHeader("Content-Type", "application/xml; charset=utf-8");
  }

  return xmlData;
});
