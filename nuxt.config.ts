// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineOrganization } from "nuxt-schema-org/schema";

export default defineNuxtConfig({
  modules: [
    "@nuxt/ui-pro",
    "@nuxt/eslint",
    "@nuxt/image",
    "@nuxt/content",
    "nuxt-tiptap-editor",
    "nuxt-auth-sanctum",
    "nuxt-laravel-echo",
    "@nuxtjs/seo",
    "vue-yandex-maps/nuxt",
    "@pinia/nuxt",
    "@vueuse/nuxt",
    "@artmizu/yandex-metrika-nuxt",
    "@sentry/nuxt/module"
  ],

  imports: {
    dirs: ["stores"]
  },

  devtools: { enabled: false },

  app: {
    head: {
      title: "Бесплатные объявления о продаже оружия бу",
      htmlAttrs: {
        lang: "ru",
        class: "scroll-smooth",
        bodyAttrs: {
          class: "antialiased"
        }
      },
      meta: [
        { charset: "utf-8" },
        { name: "viewport", content: "width=device-width, initial-scale=1" },
        { name: "og:site_name", content: "Ганпост" },
        {
          name: "description",
          content:
            "Покупка и продажа охотничьего оружия, травматических пистолетов, средств самообороны, ножей и снаряжения. Крупнейший сайт с ценами на оружие и бесплатными частными объявлениями. Купите оружие на ГанПост — быстро и безопасно."
        }
      ],
      link: [
        { rel: "icon", type: "image/x-icon", href: "https://gunpost.ru/favicon.ico" },
        { rel: "icon", type: "image/svg+xml", href: "https://gunpost.ru/favicon.svg" }
      ]
    }
  },

  css: ["~/assets/css/main.css"],

  vue: {
    compilerOptions: {
      comments: false,
      whitespace: "condense"
    }
  },

  site: {
    url: "https://gunpost.ru",
    name: "Ганпост",
    description:
      "GunPost.ru — современная онлайн‑площадка бесплатных объявлений о продаже охотничьего и стрелкового снаряжения, а также аксессуаров и товаров для активного отдыха.",

    defaultLocale: "ru"
  },

  sourcemap: {
    client: true,
    server: true
  },

  future: {
    compatibilityVersion: 4
  },

  compatibilityDate: "2024-11-27",

  nitro: {
    compressPublicAssets: true
  },

  vite: {
    optimizeDeps: {
      include: ["pusher-js"]
    },
    build: {
      chunkSizeWarningLimit: 1000,
      sourcemap: true
    },
    css: {
      devSourcemap: true
    }
  },

  typescript: {
    strict: true
  },

  hooks: {
    "pages:extend"(pages) {
      const knownCategories = [
        "hunting",
        "selfdefence",
        "pnevma",
        "optika_priceli",
        "knife",
        "zip",
        "safes",
        "kobury",
        "reloading",
        "airsoft"
      ];

      pages.push({
        name: "category",
        path: `/:category(${knownCategories.join("|")})/:type?`,
        file: "~/__listing.vue"
      });
      pages.push({
        name: "city-category",
        path: `/:city/:category(${knownCategories.join("|")})/:type?`,
        file: "~/__listing.vue"
      });
      pages.push({
        name: "city-tag",
        path: `/:city/tag/:tag`,
        file: "~/__listing.vue"
      });
      pages.push({
        name: "post",
        path: `/:category(${knownCategories.join("|")})/:slug.html`,
        file: "~/__post.vue"
      });
    }
  },

  echo: {
    broadcaster: "reverb",
    host: process.env.NUXT_PUBLIC_ECHO_HOST,
    port: process.env.NUXT_PUBLIC_ECHO_PORT,
    key: "",
    scheme: process.env.NUXT_PUBLIC_ECHO_SCHEME,
    authentication: {
      mode: "token",
      baseUrl: process.env.NUXT_PUBLIC_ECHO_AUTHENTICATION_BASE_URL,
      authEndpoint: "/broadcasting/auth"
    }
  },

  eslint: {
    config: {
      stylistic: {
        commaDangle: "never",
        braceStyle: "1tbs"
      }
    }
  },

  linkChecker: {
    enabled: false
  },

  ogImage: {
    enabled: false
  },

  robots: {
    enabled: false
  },

  sanctum: {
    endpoints: {
      csrf: "/sanctum/csrf-cookie",
      user: "/user",
      logout: "/logout"
    },
    redirect: {
      onLogin: false,
      onAuthOnly: "/"
    }
  },

  schemaOrg: {
    defaults: true,
    identity: defineOrganization({
      name: "Ганпост",
      alternateName: "GunPost",

      image: "https://gunpost.ru/logo-512.png",
      description:
        "GunPost.ru — современная онлайн‑площадка бесплатных объявлений о продаже охотничьего и стрелкового снаряжения, а также аксессуаров и товаров для активного отдыха.",

      url: "https://gunpost.ru"
    })
  },

  sentry: {
    enabled: process.env.NODE_ENV === "production",
    sourceMapsUploadOptions: {
      org: "Ганпост",
      project: "nuxt",
      authToken:
        "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"
    }
  },

  siteConfig: {
    title: "GunPost - бесплатные объявления о продаже оружия бу",
    description:
      "Покупка и продажа охотничьего оружия, травматических пистолетов, средств самообороны, ножей и снаряжения. Крупнейший сайт с ценами на оружие и бесплатными частными объявлениями. Купите оружие на ГанПост — быстро и безопасно.",
    url: "https://gunpost.ru",
    siteName: "Ганпост"
  },

  sitemap: {
    enabled: false
  },

  uiPro: {
    fonts: false,
    license: "pro"
  },

  yandexMaps: {
    apikey: "9d13e61c-a286-4717-afc1-7298f708a49a"
  },

  yandexMetrika: {
    id: "100665604",
    webvisor: true,
    consoleLog: true,
    clickmap: true,
    useCDN: true,
    trackLinks: true,
    accurateTrackBounce: true
  }
});
