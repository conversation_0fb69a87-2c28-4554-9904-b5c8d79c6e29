{"name": "gunpost", "private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=4096' nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lintfix": "prettier . --write && eslint . --fix", "typecheck": "nuxt typecheck"}, "dependencies": {"@artmizu/yandex-metrika-nuxt": "^1.1.0", "@iconify-json/lucide": "^1.2.41", "@nuxt/content": "^3.5.1", "@nuxt/eslint": "^1.3.0", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "file:nuxt-ui-pro-3.3.2.tgz", "@nuxtjs/seo": "^3.0.3", "@pinia/nuxt": "^0.11.0", "@sentry/nuxt": "^9", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-youtube": "^2.12.0", "@unovis/ts": "^1.5.1", "@unovis/vue": "^1.5.1", "@vueuse/core": "^13.1.0", "@vueuse/nuxt": "^13.1.0", "@yeger/vue-masonry-wall": "^5.0.18", "better-sqlite3": "^12.2.0", "date-fns": "^4.1.0", "import-in-the-middle": "^1.13.1", "install": "^0.13.0", "maska": "^3.1.1", "mobile-detect": "^1.4.5", "nuxt": "^3.17.2", "nuxt-auth-sanctum": "^0.6.6", "nuxt-laravel-echo": "^0.2.4", "nuxt-tiptap-editor": "^2.2.1", "photoswipe": "^5.4.4", "pinia": "^3.0.2", "pusher-js": "^8.4.0", "uuid": "^11.1.0", "valibot": "^1.1.0", "vue-photo-album": "^1.2.4", "vue-yandex-maps": "^2.1.8", "vue3-carousel": "^0.15.1"}, "devDependencies": {"@nuxt/eslint-config": "^1.3.0", "@nuxtjs/eslint-module": "^4.1.0", "@tailwindcss/postcss": "^4.1.6", "@types/uuid": "^10.0.0", "@yandex/ymaps3-types": "^1.0.16477558", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.8.3", "vue-tsc": "2.2.10"}, "packageManager": "pnpm@10.5.2", "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "@tailwindcss/oxide", "better-sqlite3", "esbuild", "maplibre-gl", "sharp", "unrs-resolver", "vue-demi"], "overrides": {"@vercel/nft": "^0.27.4"}}}