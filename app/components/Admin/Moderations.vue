<template>
  <div class="flex flex-wrap items-center gap-2">
    <UBadge icon="i-lucide-calendar" color="neutral" variant="outline">
      <NuxtTime :datetime="post.published_at" date-style="long" time-style="short" />
    </UBadge>
    <UBadge
      v-if="post.moderation_id == 0"
      icon="i-lucide-alert-circle"
      color="warning"
      variant="outline"
    >
      Ожидает модерации
    </UBadge>
    <UBadge v-if="post.moderation_id == 1" icon="i-lucide-check" color="success" variant="outline">
      Одобрено
    </UBadge>
    <UBadge v-if="post.moderation_id == 2" icon="i-lucide-x" color="error" variant="outline">
      Заблокировано
    </UBadge>
    <UButton
      v-if="post.moderation_id !== 1"
      :loading="loading.moderation"
      color="success"
      size="xs"
      @click="setModeration(post.slug, 1)"
    >
      Поставить аппрув
    </UButton>
    <UButton
      v-if="post.moderation_id !== 2"
      :loading="loading.moderation"
      color="error"
      size="xs"
      @click="setModeration(post.slug, 2)"
    >
      Заблокировать
    </UButton>
    <UButton
      icon="i-lucide-refresh-cw"
      :loading="loading.reindex"
      variant="outline"
      color="neutral"
      size="xs"
      @click="setReindex(post.slug)"
    >
      Переиндексировать
    </UButton>
    <UButton
      icon="i-lucide-edit"
      :to="{ name: 'edit-slug', params: { slug: post.slug } }"
      color="neutral"
      variant="outline"
      size="xs"
      target="_blank"
    >
      Редактировать
    </UButton>
    <UButton
      icon="i-lucide-trash"
      :loading="loading.delete"
      color="error"
      variant="outline"
      size="xs"
      target="_blank"
      @click="deletePost(post.slug)"
    >
      Удалить
    </UButton>
  </div>
</template>

<script setup lang="ts">
import { ModalsAlert } from "#components";

defineProps({
  post: {
    type: Object,
    required: true
  }
});
const toast = useToast();
const client = useSanctumClient();
const overlay = useOverlay();
const emits = defineEmits(["refresh"]);
const loading = ref({
  moderation: false,
  reindex: false,
  delete: false
});

const setModeration = async (slug: string, moderation_status: boolean) => {
  loading.value.moderation = true;

  try {
    await client(`/moderation/posts/${slug}/moderation`, {
      method: "POST",
      body: {
        moderation: moderation_status
      }
    });
    emits("refresh");
    toast.add({
      title: "Вы успешно изменили статус модерации",
      color: "success"
    });
  } catch (e) {
    console.error(e);
  }

  loading.value.moderation = false;
};
const setReindex = async (slug: string) => {
  loading.value.reindex = true;

  try {
    await client(`/moderation/posts/${slug}/index`, {
      method: "POST"
    });
    emits("refresh");

    toast.add({
      title: "Вы успешно переиндексировали объявление",
      color: "success"
    });
  } catch (e) {
    console.error(e);
  }

  loading.value.reindex = false;
};

const deletePost = (slug: string) => {
  overlay
    .create(ModalsAlert, {
      props: {
        title: "Вы точно хотите удалить объявление?",
        actions: [
          {
            label: "Да, Удалить",
            color: "error",
            icon: "i-lucide-trash",
            onClick: () => {
              deletePostAction(slug);
            }
          }
        ]
      }
    })
    .open();
};

const deletePostAction = async (slug: string) => {
  loading.value.delete = true;

  try {
    await client(`/user/posts/${slug}`, {
      method: "DELETE"
    });

    emits("refresh");
    navigateTo("/admin/moderations");

    toast.add({
      title: "Вы успешно удалили объявление",
      color: "success"
    });
  } catch (e) {
    console.error(e);
  }

  loading.value.delete = false;
};
</script>
