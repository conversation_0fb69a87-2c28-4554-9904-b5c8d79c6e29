<template>
  <UFormField size="xl" label="Фотографии" required>
    <div v-if="images?.length" id="gallery" class="my-4 flex flex-wrap gap-1.5">
      <div v-for="file in images" :key="file.src" class="relative block h-21 w-21">
        <a
          class="relative block h-21 w-21"
          :href="file.src"
          :data-pswp-width="file?.width"
          :data-pswp-height="file?.height"
        >
          <NuxtImg
            alt="gunpost.ru"
            :src="file.src"
            format="webp"
            class="z-0 h-full w-full rounded-sm object-cover"
          />
        </a>
        <UButton
          icon="i-lucide-x"
          size="xs"
          color="error"
          variant="soft"
          class="absolute top-1 right-1 z-10"
          @click.prevent="removeImage(file)"
        />
      </div>
    </div>
    <div
      class="z-10 flex cursor-pointer items-center justify-center rounded-lg border border-dashed border-gray-300 p-6"
      @click="onFileClick"
    >
      <UIcon name="i-lucide-image-plus" class="size-12 text-(--ui-text-dimmed)" />
    </div>

    <input
      ref="fileRef"
      type="file"
      multiple
      class="hidden"
      accept=".jpg, .jpeg, .png"
      @change="onFileChange"
    />
  </UFormField>
</template>

<script setup lang="ts">
import PhotoSwipeLightbox from "photoswipe/lightbox";
import "photoswipe/style.css";

const props = defineProps<{
  images: File[];
  slug: string;
}>();
const client = useSanctumClient();
const isLoading = ref(false);
const fileRef = ref<HTMLInputElement>();
const emit = defineEmits(["update"]);
const lightbox = ref(false);
/**
 * Открывает окно выбора файла.
 */
function onFileClick() {
  fileRef.value?.click();
}

/**
 * Обрабатывает изменение загруженного файла.
 * @param {Event} e - Событие изменения файла.
 */
async function onFileChange(e: Event) {
  const input = e.target as HTMLInputElement;

  if (!input.files?.length) {
    return;
  }

  const files = Array.from(input.files);
  const formData = new FormData();

  for (const file of files) {
    formData.append("photos[]", file);
  }

  await client(`/moderation/news/${props.slug}/image`, {
    method: "POST",
    body: formData
  });

  emit("update");
}

/**
 * Удаляет изображение из поста.
 * @param {{ id?: string; src?: string }} file - Объект файла.
 * @returns {Promise<void>}
 */
const removeImage = async (file) => {
  isLoading.value = true;

  await client(`/moderation/news/${props.slug}/image`, {
    method: "DELETE",
    body: {
      id: file.id
    }
  });

  isLoading.value = false;
  emit("update");
};

onMounted(() => {
  lightbox.value = new PhotoSwipeLightbox({
    gallery: "#gallery",
    children: "a",
    pswpModule: () => import("photoswipe")
  });
  lightbox.value.init();
});

onUnmounted(() => {
  if (lightbox.value) {
    lightbox.value.destroy();
    lightbox.value = null;
  }
});
</script>
