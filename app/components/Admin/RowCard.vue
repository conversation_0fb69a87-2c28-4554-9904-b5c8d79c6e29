<template>
  <UCard :disabled="isLoading" :ui="{ body: 'space-y-4 p-2 sm:p-2', footer: 'p-2 sm:p-2' }">
    <div class="relative flex w-full gap-4">
      <div class="absolute top-1 right-1">
        <UDropdownMenu
          v-if="!isBanned"
          :items="items"
          :disabled="isLoading"
          type="checkbox"
          :content="{
            align: 'end',
            side: 'bottom',
            sideOffset: 8
          }"
        >
          <UButton icon="i-lucide-menu" color="neutral" variant="outline" />
        </UDropdownMenu>
      </div>
      <AdminGallery
        :images="post?.images"
        class-name="h-24"
        :class="{
          'rounded-md bg-yellow-100 p-1 dark:bg-yellow-950': hasColorPromo
        }"
      />
      <div class="space-y-2 pr-10">
        <div class="space-x-1">
          <UIcon v-if="!isPublished" name="i-lucide-eye-off" />
          <UTooltip v-if="hasVipPromo" text="Это VIP объявление">
            <UBadge size="xs" icon="i-lucide-gem" color="neutral" variant="outline" />
          </UTooltip>
          <UTooltip v-if="hasColorPromo" text="Это объявление выделено цветом">
            <UBadge
              size="xs"
              icon="i-lucide-paintbrush-vertical"
              color="neutral"
              variant="outline"
            />
          </UTooltip>
          <span class="font-medium md:line-clamp-2">
            {{ post.title }}
          </span>
        </div>
        <span class="block text-lg font-semibold">
          {{ $currency(post.price) }}
        </span>
        <span
          class="text-md line-clamp-4 block text-[var(--ui-text-muted)] md:line-clamp-2 md:text-sm"
        >
          <UIcon name="i-lucide-map-pin" class="size-2.5" />
          {{ post.address ?? "Адрес не указан" }}
        </span>
      </div>
    </div>
    <template #footer>
      <div class="flex flex-wrap items-center justify-start gap-1.5">
        <UBadge v-if="isProcessing">
          Обработка объявления: {{ post.sourceGunsBroker?.percentage }}%
        </UBadge>

        <!-- Статус публикации -->
        <template v-if="!isPublished && !isProcessing">
          <UBadge color="neutral" variant="outline">Не опубликовано</UBadge>
          <UButton size="xs" color="success" @click="publishedPostAction"> Опубликовать </UButton>
        </template>

        <template v-else-if="!isBanned">
          <UBadge color="success" variant="outline">Опубликовано</UBadge>
        </template>

        <!-- Кнопки действий -->
        <template v-if="canOpen">
          <UButton
            size="xs"
            color="neutral"
            variant="outline"
            icon="i-lucide-link"
            :to="{
              name: 'post',
              params: { category: post.category.slug, slug: post.slug }
            }"
            target="_blank"
            label="Открыть"
          />
        </template>

        <UButton
          v-else
          size="xs"
          color="neutral"
          variant="outline"
          icon="i-lucide-link"
          :to="{
            name: 'post',
            params: { category: post.category.slug, slug: post.slug },
            query: { preview: true }
          }"
          target="_blank"
          label="Открыть предпросмотр"
        />

        <!-- Статусы модерации -->
        <template v-if="isBanned">
          <UBadge icon="i-lucide-alert-circle" color="error" variant="outline">
            Заблокировано
          </UBadge>
          <UButton size="xs" color="error" @click="deletePost"> Удалить </UButton>
        </template>

        <template v-if="isPublished && isNotApproved">
          <UBadge icon="i-lucide-alert-circle" color="warning" variant="outline">
            На модерации
          </UBadge>
        </template>

        <!-- Статистика -->
        <UBadge v-if="post.views > 10" icon="i-lucide-eye" color="neutral" variant="outline">
          {{ post.views }}
        </UBadge>
        <UBadge v-if="post.favorites > 10" icon="i-lucide-star" color="neutral" variant="outline">
          {{ post.favorites }}
        </UBadge>
      </div>
    </template>

    <ModalsArchivedReasonModal
      v-if="archivedModal"
      :loading="isLoading"
      @set="publishedPostAction"
      @close="archivedModal = false"
    />
  </UCard>
</template>

<script setup lang="ts">
import { ModalsAlert } from "#components";

const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});
const client = useSanctumClient();
const { $currency } = useNuxtApp();
const toast = useToast();
const emits = defineEmits(["refresh"]);
const isLoading = ref(false);
const archivedModal = ref(false);

const overlay = useOverlay();

// Вычисляемые свойства для определения состояния поста
const isPublished = computed(() => Boolean(props.post.published_at));
const isBanned = computed(() => props.post.moderation === "banned");
const isApproved = computed(() => props.post.moderation === "approved");
const isNotApproved = computed(() => props.post.moderation === "not_approved");
// const canPreview = computed(() => !isBanned.value && isPublished.value);
const canOpen = computed(() => isPublished.value && isApproved.value);
const isProcessing = computed(
  () => props.post?.sourceGunsBroker && props.post?.sourceGunsBroker?.percentage >= 0
);
const items = computed(() => {
  const result = [];

  if (!isPublished.value && !isProcessing.value) {
    result.push([
      {
        label: "Опубликовать",
        icon: "i-lucide-eye",
        onSelect: () => publishedPostAction()
      }
    ]);
  }

  if (isPublished.value) {
    result.push([
      {
        label: "Снять с публикации",
        icon: "i-lucide-eye-closed",
        onSelect: () => (archivedModal.value = true)
      }
    ]);
  }

  if (!isProcessing.value) {
    result.push([
      {
        label: "Редактировать",
        icon: "i-lucide-pencil",
        to: `/edit/${props.post.slug}`
      },
    ]);
  }

  result.push([

    {
      label: "Удалить",
      icon: "i-lucide-trash",
      color: "error" as const,
      onSelect: () => deletePost()
    }
  ]);

  return result;
});

const deletePost = () => {
  overlay
    .create(ModalsAlert, {
      props: {
        title: props.post?.title,
        description: "Вы точно хотите удалить объявление?",
        actions: [
          {
            label: "Да, Удалить",
            color: "error",
            icon: "i-lucide-trash",
            onClick: () => {
              deletePostAction();
            }
          }
        ]
      }
    })
    .open();
};

const hasColorPromo = computed(() =>
  props.post?.promotions?.find((promo) => promo.type?.type === "color")
);
const hasVipPromo = computed(() =>
  props.post?.promotions?.find((promo) => promo.type?.type === "vip")
);

const deletePostAction = async () => {
  isLoading.value = true;
  try {
    await client(`/user/posts/${props.post.slug}`, {
      method: "DELETE"
    });
    emits("refresh");
  } catch (e) {
    console.error(e);
  }

  isLoading.value = false;
};

const publishedPostAction = async (reason?: object) => {
  isLoading.value = true;
  try {
    await client(`/user/posts/${props.post.slug}/approve`, {
      method: "POST",
      body: reason
    });

    if (!props.post?.published_at) {
      toast.add({
        title: "Вы успешно опубликовали объявление",
        color: "success",
        actions: [
          {
            label: "Перейти в объявления",
            color: "neutral",
            to: `/${props.post.category.slug}/${props.post.slug}.html`,
            target: "_blank"
          }
        ]
      });
    } else {
      toast.add({
        title: "Вы успешно сняли объявление",
        color: "success"
      });
    }

    emits("refresh");
  } catch (e) {
    toast.add({
      title: e.data?.message ?? "Произошла ошибка",
      description: Object.values(e.data?.errors ?? {})
        .flat()
        .join(", "),
      color: "error"
    });
  }

  archivedModal.value = false;
  isLoading.value = false;
};
</script>
