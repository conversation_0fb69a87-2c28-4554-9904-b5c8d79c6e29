<template>
  <div>
    <pre class="max-w-2xl overflow-x-auto">{{ item.check.result.result_json }}</pre>
    <div
      v-if="item.check && item.check.status === 'done'"
      class="flex flex-wrap items-center gap-2"
    >
      <!-- Уникальность -->
      <UBadge variant="soft" :color="getUniqueColor(item.check.text_unique)">
        Уникальность: {{ item.check.text_unique }}%
      </UBadge>

      <!-- Всего символов -->
      <UBadge variant="soft">
        Всего символов: {{ item.check.seo_check.count_chars_with_space }}
      </UBadge>

      <UBadge variant="soft"
        >Индекс удобочитаемости: {{ item.check.seo_check.flesch_reading_ease }}
      </UBadge>

      <!-- Индекс туманности Ганнинга -->
      <UBadge variant="soft" :color="getGunningColor(item.check.seo_check.gunning_fog_index)">
        Индекс туманности Ганнинга: {{ item.check.seo_check.gunning_fog_index }}
      </UBadge>

      <!-- Без пробелов -->
      <UBadge variant="soft">
        Без пробелов: {{ item.check.seo_check.count_chars_without_space }}
      </UBadge>

      <!-- Количество слов -->
      <UBadge variant="soft"> Количество слов: {{ item.check.seo_check.count_words }} </UBadge>

      <!-- Спамность -->
      <UBadge variant="soft" :color="getSpamColor(item.check.seo_check.spam_percent)">
        Спамность: {{ item.check.seo_check.spam_percent }}%
      </UBadge>

      <!-- Вода -->
      <UBadge variant="soft" :color="getWaterColor(item.check.seo_check.water_percent)">
        Вода: {{ item.check.seo_check.water_percent }}%
      </UBadge>
    </div>
    <div v-else>
      <UBadge variant="soft" color="primary">
        <UIcon name="i-lucide-loader" class="animate-spin" />
        Проверка не завершена
      </UBadge>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  item: {
    type: Object,
    required: true
  }
});

/**
 * Возвращает цвет для показателя «Уникальность»
 * зелёный (success) — ≥ 95%
 * жёлтый (warning) — 70…95%
 * красный (error) — < 70%
 */
function getUniqueColor(percent) {
  if (percent >= 95) return "success";
  if (percent >= 70) return "warning";
  return "error";
}

/**
 * Возвращает цвет для показателя «Спамность»
 * зелёный — < 30%
 * жёлтый — 30…60%
 * красный — > 60%
 */
function getSpamColor(percent) {
  if (percent < 30) return "success";
  if (percent <= 60) return "warning";
  return "error";
}

/**
 * Возвращает цвет для показателя «Вода»
 * зелёный — < 15%
 * жёлтый — 15…30%
 * красный — > 30%
 */
function getWaterColor(percent) {
  if (percent < 15) return "success";
  if (percent <= 30) return "warning";
  return "error";
}

/**
 * Возвращает цвет для Gunning Fog Index:
 * – до 6      (простое чтение)          — success (зелёный)
 * – 7 – 12    (комфортно для взрослых)  — success (зелёный)
 * – 13 – 17   (средняя сложность)       — warning (жёлтый)
 * – 18 – 24   (высокая сложность)       — warning (жёлтый)
 * – более 24  (трудно даже экспертам)   — error   (красный)
 */
function getGunningColor(index) {
  if (index <= 12) return "success";
  if (index <= 17) return "warning";
  if (index <= 24) return "warning";
  return "error";
}
</script>
