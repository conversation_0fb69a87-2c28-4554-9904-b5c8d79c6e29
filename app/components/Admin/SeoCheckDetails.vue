<template>
  <UCard v-if="item.check">
    <div class="space-y-4">
      <AdminSeoCheck :item="item" />

      <UTable
        v-if="props.item.check?.result?.urls.length"
        sticky
        :columns="columns"
        :data="props.item.check?.result.urls"
        class="max-h-[312px] flex-1"
      />

      <div v-if="item.check?.spell_check?.length" class="space-y-2">
        <h3 class="text-md font-semibold">Орфографические ошибки</h3>
        <div v-for="(row, type) in item.check.spell_check" :key="type" class="text-sm">
          <span class="mr-1">{{ row.reason }}</span>
          <span class="font-bold">{{ row.error_text }}</span>
          <div class="mt-1 flex flex-wrap gap-1">
            <UBadge
              v-for="error in row.replacements"
              :key="error"
              size="sm"
              color="error"
              variant="outline"
            >
              {{ error }}
            </UBadge>
          </div>
        </div>
      </div>

      <section>
        <h3 class="text-md font-semibold">Рекомендации автору</h3>
        <ul class="list-disc space-y-1 pl-6">
          <li v-for="(insight, i) in getSeoInsightsForAuthor(item.check.seo_check)" :key="i">
            {{ insight }}
          </li>
        </ul>
      </section>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { TableColumn } from "@nuxt/ui";

const UBadge = resolveComponent("UBadge");
const ULink = resolveComponent("ULink");
const props = defineProps({
  item: {
    type: Object,
    required: true
  }
});

interface SeoRow {
  url: string;
  plagiat: number;
}

const columns: TableColumn<SeoRow>[] = [
  {
    accessorKey: "plagiat",
    header: "Плагиат",
    cell: ({ row }) => {
      const plagiat = Number(row.getValue("plagiat"));
      const color = plagiat >= 20 ? "error" : plagiat >= 10 ? "warning" : "success";

      return h(
        UBadge,
        { class: "capitalize", variant: "subtle", color },
        () => `${plagiat.toFixed(2)}%`
      );
    }
  },
  {
    accessorKey: "url",
    header: "URL",
    cell: ({ row }) => {
      const url = row.original.url;
      return h(ULink, { variant: "subtle", size: "xs", target: "_blank", to: url }, () => url);
    }
  }
];

function getSeoInsightsForAuthor(seoCheck) {
  const insights: string[] = [];
  const frequentKeys = seoCheck.list_keys.filter((k) => k.count >= 4);

  if (frequentKeys.length > 0) {
    const top = frequentKeys.map((k) => `«${k.key_title}» (${k.count} раз)`).join(", ");
    insights.push(
      `В тексте хорошо представлены ключевые слова: ${top}. Это усиливает семантику статьи.`
    );
  }

  const underusedKeys = seoCheck.list_keys.filter((k) => k.count === 1 && k.key_title.length > 4);
  if (underusedKeys.length > 0) {
    const candidates = underusedKeys
      .slice(0, 5)
      .map((k) => `«${k.key_title}»`)
      .join(", ");
    insights.push(`Можно усилить следующие термины, упомянув их ещё раз: ${candidates}.`);
  }

  const complexGroups = seoCheck.list_keys_group.filter((g) => g.sub_keys.length >= 3);
  if (complexGroups.length > 0) {
    for (const group of complexGroups.slice(0, 2)) {
      insights.push(
        `Группа «${group.key_title}» хорошо раскрыта в связке с: ${group.sub_keys.map((k) => k.key_title).join(", ")}.`
      );
    }
  }

  if (seoCheck.water_percent > 20) {
    insights.push(
      `Вода составляет ${seoCheck.water_percent}%. Подумай, можно ли сделать текст более конкретным.`
    );
  }

  if (seoCheck.spam_percent > 50) {
    insights.push(
      `Вероятно, текст содержит слишком много повторений. Спамность: ${seoCheck.spam_percent}%. Попробуй переформулировать некоторые фразы.`
    );
  }

  if (insights.length === 0) {
    insights.push("Текст сбалансирован по ключевым словам. Отличная работа!");
  }

  return insights;
}
</script>
