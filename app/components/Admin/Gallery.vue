<template>
  <div class="w-32 shrink-0 overflow-hidden">
    <div
      v-if="!hovered"
      class="no-repeat w-full rounded-sm bg-cover bg-center"
      :class="className"
      :style="`background-image: url(${cover});`"
      @mouseover="mouseover"
    />

    <Carousel v-else v-bind="carouselConfig" @mouseleave="hovered = false">
      <Slide v-for="(img, i) in images" :key="i">
        <NuxtImg
          :src="img.src"
          alt="gunpost.ru"
          format="webp"
          class="w-full rounded-sm object-cover"
          :class="className"
        />
      </Slide>
      <template #addons>
        <Pagination />
      </template>
    </Carousel>
  </div>
</template>

<script setup lang="ts">
import { Carousel, Slide, Pagination } from "vue3-carousel";

const props = defineProps<{
  alt?: string;
  className?: string;
  images: { src: string }[];
}>();
const hovered = ref(false);
const cover = props.images[0]?.src || "/images/post.jpg";
const carouselConfig = {
  itemsToShow: 1,
  autoplay: 1000
};
const mouseover = () => {
  hovered.value = props.images.length > 1;
};
</script>
