<template>
  <UCard>
    <div class="relative flex w-full gap-4">
      <AdminGallery :images="post?.images" class-name="h-24" />
      <div class="space-y-2">
        <div class="flex items-center gap-1.5">
          <UIcon v-if="!post.published_at" name="i-lucide-eye-off" />
          <UIcon v-if="post.published_at" name="i-lucide-eye" />
          <span class="font-medium md:line-clamp-2">{{ post.title }}</span>
        </div>
        <span class="block text-lg font-semibold">
          {{ $currency(post.price) }}
        </span>
        <span
          class="text-md line-clamp-4 block text-[var(--ui-text-muted)] md:line-clamp-2 md:text-sm"
        >
          <UIcon name="i-lucide-map-pin" class="size-2.5" />
          {{ post.address ?? "Адрес не указан" }}
        </span>
      </div>
    </div>
    <template #footer>
      <div class="flex flex-wrap items-center gap-2">
        <UButton
          size="xs"
          color="neutral"
          variant="outline"
          icon="i-lucide-link"
          :to="{
            name: 'post',
            params: { category: post.category.slug, slug: post.slug },
            query: { preview: 'true' }
          }"
          target="_blank"
          label="Открыть"
        />
        <AdminModerations :post="post" @refresh="$emit('refresh')" />
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
defineProps({
  post: {
    type: Object,
    required: true
  }
});
defineEmits(["refresh"]);
</script>
