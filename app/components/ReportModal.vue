<template>
  <UModal v-model:open="open" title="Пожаловаться на объявление">
    <UButton color="error" variant="soft" block icon="i-lucide-flag">
      Пожаловаться на объявление
    </UButton>
    <template #body>
      <div>
        <UForm ref="form" class="space-y-4" :state="state" @submit="sendReport">
          <UFormField required name="message" size="xl" label="Ваш комментарий">
            <UTextarea
              v-model="state.message"
              class="w-full"
              placeholder="Подробно опишите почему модератору стоит обратить внимание на это объявление?"
            />
          </UFormField>

          <UFormField v-if="!isAuthenticated" name="email" required size="xl">
            <UInput v-model="state.email" placeholder="Ваш email" class="w-full" />
          </UFormField>

          <p v-if="!isAuthenticated" class="text-sm text-[var(--ui-text-muted)]">
            Продолжая, вы принимаете
            <a
              class="text-[var(--ui-text-highlighted)] hover:underline"
              target="_blank"
              href="/terms"
              >пользовательское соглашение</a
            >,
            <a
              class="text-[var(--ui-text-highlighted)] hover:underline"
              target="_blank"
              href="/oferta"
              >публичную оферту</a
            >
            и даёте
            <a
              class="text-[var(--ui-text-highlighted)] hover:underline"
              target="_blank"
              href="/privacy_policy"
              >согласие</a
            >
            на обработку персональных данных
          </p>

          <UButton size="xl" block color="error" type="submit"> Отправить жалобу </UButton>
        </UForm>
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import type { Post } from "~/types/post";

const { isAuthenticated } = useSanctumAuth();
const client = useSanctumClient();
const toast = useToast();
const props = defineProps<{
  post: Post;
}>();
const open = ref(false);
const state = reactive({
  message: "",
  email: ""
});
const form = useTemplateRef("form");
const sendReport = async () => {
  try {
    await client(`/posts/${props.post.slug}/report`, {
      method: "POST",
      body: {
        message: state.message,
        email: state.email
      }
    });

    open.value = false;

    state.message = "";
    state.email = "";

    toast.add({
      title: "Ваша жалоба успешно отправлена",
      description: "Наши модераторы рассмотрят её в ближайшее время"
    });
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      form.value?.setErrors(err.bag);
    }
  }
};
</script>
