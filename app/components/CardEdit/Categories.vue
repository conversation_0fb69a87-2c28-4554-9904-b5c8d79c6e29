<template>
  <UCard :ui="{ body: 'space-y-4' }">
    <UFormField label="Выберите категорию:" size="xl" required>
      <div class="mt-4 space-y-2">
        <UButton
          v-for="item in categories"
          :key="item.category"
          :color="modelValue?.slug === item?.category ? 'primary' : 'neutral'"
          :variant="modelValue?.slug === item?.category ? 'soft' : 'outline'"
          size="sm"
          class="relative flex w-full p-2.5 pl-3.5 ring-gray-200 hover:ring-1 dark:ring-gray-800"
          @click.prevent="selectCategory(item)"
        >
          <h3 class="pr-12 text-sm leading-tight">
            {{ item.label }}
          </h3>
          <NuxtImg
            class="absolute right-0 bottom-0 z-10 h-full"
            :alt="`Иконка категории ${item.label}`"
            :src="item.image"
          />
        </UButton>
      </div>
    </UFormField>
    <template v-if="is_active" #footer>
      <div class="flex gap-2">
        <UButton
          size="xl"
          type="submit"
          color="success"
          :disabled="!modelValue"
          @click="selectCategory(modelValue)"
        >
          Продолжить
        </UButton>
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
import type { Category } from "~/types/post";

defineProps<{
  modelValue: Category | undefined;
  is_active: boolean | false;
}>();
const emit = defineEmits(["update:modelValue"]);
const { categories } = useDashboard();
const selectCategory = (item: {
  label: string;
  category: string;
  registration?: boolean;
  image: string;
  colSpan: string;
}) => {
  emit("update:modelValue", {
    name: item.label,
    slug: item.category,
    registration: item.registration,
    value: item.category,
    label: item.label
  });
};
</script>
