<template>
  <UFormField ref="form" size="xl" label="Фотографии" required>
    <div v-if="modelValue?.length" id="gallery" class="my-4 flex flex-wrap gap-1.5">
      <div v-for="file in modelValue" :key="file.src" class="relative block h-21 w-21">
        <a
          class="relative block h-21 w-21"
          :href="file.src"
          :data-pswp-width="file?.width"
          :data-pswp-height="file?.height"
        >
          <img
            :src="file.src"
            alt="gunpost.ru"
            class="z-0 h-full w-full rounded-sm object-cover"
            :class="{ 'pointer-events-none': file?.loading && disabled }"
          />
          <span
            v-if="file?.loading"
            class="absolute top-0 left-0 flex h-full w-full items-center justify-center bg-gray-100/40"
          >
            <UIcon
              name="i-lucide-loader-circle"
              class="size-8 animate-spin text-[var(--ui-text-muted)]"
            />
          </span>
        </a>
        <UButton
          v-if="!file?.loading && !disabled"
          icon="i-lucide-x"
          size="xs"
          color="error"
          variant="soft"
          class="absolute top-1 right-1 z-10"
          @click.prevent="removeImage(file)"
        />
      </div>
    </div>
    <div
      class="z-10 flex cursor-pointer items-center justify-center rounded-lg border border-dashed border-gray-300 p-6"
      @click="onFileClick"
    >
      <UIcon name="i-lucide-image-plus" class="size-12 text-(--ui-text-dimmed)" />
    </div>

    <input
      ref="fileRef"
      type="file"
      name="images[]"
      multiple
      class="hidden"
      accept=".jpg, .jpeg, .png"
      @change="onFileChange"
    />
  </UFormField>
</template>

<script setup lang="ts">
import * as Sentry from "@sentry/nuxt";
import PhotoSwipeLightbox from "photoswipe/lightbox";
import "photoswipe/style.css";
import type { PostImage } from "~/types/post";

const props = defineProps<{
  modelValue: PostImage[];
  slug: string;
  disabled?: boolean;
}>();
const { $metrika } = useNuxtApp();
const client = useSanctumClient();
const isLoading = ref(false);
const fileRef = ref<HTMLInputElement>();
const form = useTemplateRef("form");
const emit = defineEmits(["update:modelValue"]);
const lightbox = ref<InstanceType<typeof PhotoSwipeLightbox> | null>(null);
/**
 * Открывает окно выбора файла.
 */
function onFileClick() {
  $metrika.reachGoal("add_step_input_click_image");
  fileRef.value?.click();
}

/**
 * Обрабатывает изменение загруженного файла.
 */
async function onFileChange(e: Event) {
  $metrika.reachGoal("add_step_image");
  const input = e.target as HTMLInputElement;

  if (!input.files?.length) {
    return;
  }

  const newModelValue = [...props.modelValue];
  const files = Array.from(input.files);

  for (const file of files) {
    try {
      const src = URL.createObjectURL(file);

      // Add a default dimension in case image loading fails
      let width = 0;
      let height = 0;

      try {
        const dimensions = await new Promise<{ width: number; height: number }>((resolve) => {
          const img = new Image();

          // Set timeout to handle cases where onload never fires
          const timeout = setTimeout(() => {
            resolve({ width: 0, height: 0 });
          }, 1000);

          img.onload = () => {
            clearTimeout(timeout);
            resolve({ width: img.width, height: img.height });
          };

          img.onerror = () => {
            clearTimeout(timeout);
            resolve({ width: 0, height: 0 });
          };

          img.src = src;
        });

        width = dimensions.width;
        height = dimensions.height;
      } catch (error) {
        console.error("Error getting image dimensions:", error);
        // Continue with default dimensions
      }

      newModelValue.push({
        file,
        src,
        width,
        height,
        loading: false
      });
    } catch (error) {
      console.error("Error processing file:", file.name, error);
      // Continue with next file if one fails
    }
  }

  // Reset input value to allow selecting the same file again
  if (input) {
    input.value = "";
  }

  if (newModelValue.length > 0) {
    emit("update:modelValue", newModelValue);
  }
}

/**
 * Удаляет изображение из поста.
 */
const removeImage = async (file: PostImage) => {
  if (!file?.id) {
    const images = props.modelValue.filter((img) => img.src !== file.src);
    emit("update:modelValue", images);
    return;
  }

  isLoading.value = true;

  file.loading = true;
  try {
    await client(`/post/${props.slug}/image`, {
      method: "DELETE",
      body: {
        id: file.id
      }
    });
    const newModelValue = props.modelValue.filter((image) => image.src !== file.src);
    emit("update:modelValue", newModelValue);
  } catch (error) {
    console.log(error);
    Sentry.captureException(error);
    useSanctumError(error);
  }
  file.loading = false;
  isLoading.value = false;
};

onMounted(() => {
  lightbox.value = new PhotoSwipeLightbox({
    gallery: "#gallery",
    children: "a",
    pswpModule: () => import("photoswipe")
  });
  lightbox.value.init();
});

onUnmounted(() => {
  if (lightbox.value) {
    lightbox.value.destroy();
    lightbox.value = null;
  }
});
</script>
