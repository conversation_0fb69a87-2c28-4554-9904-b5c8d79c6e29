<template>
  <UCard :ui="{ body: 'space-y-4' }">
    <UFormField
      required
      size="xl"
      name="title"
      label="Название"
      description="Придумайте название, чтобы покупатели поняли что вы предлагаете. Не пишите слово характеристики в названии объявлениия"
    >
      <UInput
        :value="modelValue"
        autofocus
        class="w-full"
        placeholder="Например: Сайга 12С"
        @input="emit('update:modelValue', $event.target.value)"
      />
    </UFormField>
    <template v-if="is_active" #footer>
      <div class="flex gap-2">
        <UButton size="xl" type="submit" :loading="isLoading" color="success"> Продолжить </UButton>
        <UButton size="xl" variant="link" color="neutral" @click="$emit('back')"> Назад </UButton>
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
defineProps({
  modelValue: String,
  is_active: Boolean,
  isLoading: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(["update:modelValue", "back"]);
</script>
