<template>
  <div v-if="post?.thumbnails && post?.thumbnails?.length > 0">
    <div class="flex h-72 max-h-120 gap-2 overflow-hidden rounded-lg md:h-72 lg:h-90">
      <!-- Основное изображение -->
      <div
        class="relative grid w-full cursor-pointer grid-cols-1 grid-rows-1 gap-4"
        :class="mainImageClass"
        @click="openGallery"
      >
        <NuxtImg
          :src="post.thumbnails?.[0]"
          alt="gunpost.ru"
          fetchpriority="high"
          format="webp"
          class="h-full w-full object-cover object-center"
        />
        <div
          v-if="post.thumbnails.length > 1"
          class="absolute right-2 bottom-2 text-center md:hidden"
        >
          <UButton color="neutral" size="sm" variant="subtle" icon="i-lucide-grip">
            Показать все фото
          </UButton>
        </div>
        <div
          class="absolute inset-0 bg-black/30 opacity-0 transition-opacity duration-300 hover:opacity-100"
        />
      </div>

      <!-- Миниатюры -->
      <div v-if="post.thumbnails.length > 1" class="hidden gap-2 md:grid" :class="gridClass">
        <template v-for="(image, index) in limitedImages" :key="index">
          <div class="relative h-full w-full cursor-pointer" @click="openGallery">
            <NuxtImg
              alt="gunpost.ru"
              :src="image"
              class="h-full w-full object-cover object-center"
            />
            <div
              class="absolute inset-0 bg-black/30 opacity-0 transition-opacity duration-300 hover:opacity-100"
            />
          </div>
        </template>

        <div
          v-if="hasMoreImages"
          class="relative h-full w-full cursor-pointer"
          @click="openGallery"
        >
          <NuxtImg
            :src="limitedImages[limitedImages.length - 1]"
            alt="gunpost.ru"
            class="h-full w-full object-cover object-center"
          />
          <div
            class="absolute inset-0 bg-black/30 opacity-0 transition-opacity duration-300 hover:opacity-100"
          />
          <div class="absolute bottom-2 left-1/2 w-[calc(100%-2rem)] -translate-x-1/2 text-center">
            <UButton
              color="neutral"
              block
              size="sm"
              class="lg:py-2.5"
              variant="subtle"
              icon="i-lucide-grip"
            >
              Показать все фото
            </UButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Галерея для случая без модального окна -->
    <div v-if="!hasManyPhotos" class="hidden">
      <PhotoAlbum
        id="gallery"
        layout="rows"
        :photos="processedImages"
        :photo-renderer="CustomPhotoSwipeAdapter"
      />
    </div>

    <!-- Модальное окно с галереей -->
    <UModal v-model:open="open" fullscreen :dismissible="isModalCanBeClosed">
      <template #header>
        <div class="flex w-full items-center justify-between gap-4">
          <UButton
            class="hover:underline"
            variant="link"
            square
            color="neutral"
            icon="i-lucide-arrow-left"
            @click="open = false"
          />
          <div v-if="post" class="flex gap-2">
            <CardShare :post="post" />
            <CardFavorite :post="post" :is_favorite="is_favorite" @toggle="$emit('toggle')" />
          </div>
        </div>
      </template>
      <template #body>
        <UContainer class="max-w-3xl pb-4">
          <PhotoAlbum
            id="gallery"
            layout="rows"
            :photos="processedImages"
            :photo-renderer="CustomPhotoSwipeAdapter"
          />
        </UContainer>
      </template>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { PhotoAlbum } from "vue-photo-album";
import PhotoSwipeLightbox from "photoswipe/lightbox";
import CustomPhotoSwipeAdapter from "@/components/CustomPhotoSwipeAdapter.vue";
import "photoswipe/style.css";
import type { Post } from "~/types/post";

const props = defineProps<{
  post: Post;
  is_favorite?: boolean;
}>();

defineEmits(["toggle"]);

// Состояния
const open = ref(false);
const isModalCanBeClosed = ref(true);
const lightbox = ref<PhotoSwipeLightbox | null>(null);

// Вычисляемые свойства
const limitedImages = computed(() => props.post.thumbnails.slice(1, 4));
const hasMoreImages = computed(() => props.post.thumbnails.length >= 4);
const hasManyPhotos = computed(() => props.post.images.length > 5);

const mainImageClass = computed(() => {
  const length = props.post.thumbnails.length;
  if (length > 1 && length < 4) return "md:w-3/5";
  if (length >= 4) return "md:w-2/5";
  return "md:w-full";
});

const gridClass = computed(() => {
  const count = Math.min(props.post.thumbnails.length - 1, 4);
  return {
    1: "grid-cols-1 grid-rows-1 w-full",
    2: "grid-cols-1 grid-rows-2 w-2/4",
    3: "grid-cols-2 grid-rows-2 w-3/4",
    4: "grid-cols-2 grid-rows-2 w-3/4"
  }[count];
});

// Обработка изображений для PhotoSwipe
const processedImages = computed(() => {
  return props.post.images.map((image) => ({
    src: image.src.startsWith("http") ? image.src : `/${image.src}`,
    width: image.width,
    height: image.height
  }));
});

// Инициализация и управление PhotoSwipe
const initPhotoSwipe = () => {
  if (lightbox.value) {
    lightbox.value.destroy();
  }

  lightbox.value = new PhotoSwipeLightbox({
    gallery: "#gallery",
    children: "a",
    pswpModule: () => import("photoswipe")
  });

  lightbox.value.init();
};

const destroyPhotoSwipe = () => {
  if (lightbox.value) {
    lightbox.value.destroy();
    lightbox.value = null;
  }
};

// Обработка открытия/закрытия модального окна
watch(open, async (value) => {
  if (value) {
    await nextTick();
    initPhotoSwipe();
  } else {
    destroyPhotoSwipe();
  }
});

// Наблюдатель для определения возможности закрытия модального окна
let observer = null;

onMounted(() => {
  observer = new MutationObserver(() => {
    const dismissableDiv = document.querySelector("div.pswp--ui-visible");
    isModalCanBeClosed.value = !dismissableDiv;
  });

  observer.observe(document.body, { childList: true, subtree: true });
});

onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect();
  }
  destroyPhotoSwipe();
});

const openGallery = () => {
  if (hasManyPhotos.value) {
    open.value = true;
  } else {
    const lightbox = new PhotoSwipeLightbox({
      dataSource: processedImages.value.map((img) => ({
        src: img.src,
        width: img.width,
        height: img.height
      })),
      pswpModule: () => import("photoswipe")
    });
    lightbox.init();
    lightbox.loadAndOpen(0);
  }
};
</script>
