<template>
  <UModal v-model:open="open">
    <span class="cursor-pointer text-[var(--ui-text-muted)] underline" @click="open = true">{{
      address
    }}</span>

    <template #content>
      <yandex-map
        v-model="map"
        :settings="{
          location: {
            center: [coords?.lon, coords?.lat],
            zoom: 12
          }
        }"
        width="100%"
        height="250px"
      >
        <yandex-map-default-scheme-layer />
        <yandex-map-default-features-layer />

        <yandex-map-marker
          :settings="{ coordinates: [coords?.lon, coords?.lat] }"
          position="top-center left-center"
        >
          <UIcon name="i-lucide-locate-fixed" class="text-primary size-10" />
        </yandex-map-marker>
      </yandex-map>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { shallowRef } from "vue";
import type { YMap } from "@yandex/ymaps3-types";
import {
  <PERSON><PERSON>xMap,
  YandexMapDefaultScheme<PERSON><PERSON><PERSON>,
  <PERSON>dexMapDefault<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dexMapMarker
} from "vue-yandex-maps";

defineProps<{
  address: string;
  coords: {
    lat: number;
    lon: number;
  };
}>();

const open = ref(false);
const map = shallowRef<null | YMap>(null);
</script>
