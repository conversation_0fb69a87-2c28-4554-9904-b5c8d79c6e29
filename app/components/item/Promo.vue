<template>
  <div class="mt-4 mb-4 space-y-4 border-b border-[var(--ui-border)] pb-4">
    <h3 class="mb-2 text-lg font-semibold text-(--ui-text-highlighted)">Платное продвижение</h3>
    <ul v-if="promotions?.data?.length" class="space-y-2">
      <li
        v-for="(promo, index) in promotions.data"
        :key="index"
        class="text-xs text-(--ui-text-muted)"
      >
        <span class="flex items-center gap-1">
          <UIcon v-if="promo.type.type === 'vip'" class="text-success-500" name="i-lucide-gem" />
          <UIcon
            v-if="promo.type.type === 'up'"
            class="text-success-500"
            name="i-lucide-trending-up"
          />
          <UIcon
            v-if="promo.type.type === 'color'"
            class="text-success-500"
            name="i-lucide-paintbrush-vertical"
          />
          {{ promo.type.name }}
        </span>
        <NuxtTime :datetime="promo.purchased_at" date-style="long" time-style="short" />
        <span v-if="promo.duration > 1"> на {{ promo.duration }} дней </span>
      </li>
    </ul>
    <PromoActions :post="post" :types="promotions.types" size="md" @refresh="refresh" />
  </div>
</template>

<script setup lang="ts">
import type { Post } from "~/types/post";

const props = defineProps<{
  post: Post;
}>();
const client = useSanctumClient();
const { data: promotions, refresh } = await useAsyncData(`post:promotions:${props.post.slug}`, () =>
  client(`/posts/${props.post.slug}/promotions`)
);
</script>
