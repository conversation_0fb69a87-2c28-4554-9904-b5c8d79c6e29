<template>
  <div class="relative grid w-full cursor-pointer grid-cols-1 grid-rows-1 gap-4" :class="className">
    <img :src="image" alt="Main image" class="h-full w-full object-cover object-center" />
    <div v-if="hasMultiple" class="absolute right-2 bottom-2 text-center md:hidden">
      <UButton color="neutral" size="sm" variant="subtle" icon="i-lucide-grip">
        Показать все фото
      </UButton>
    </div>
    <div
      class="absolute inset-0 bg-black/30 opacity-0 transition-opacity duration-300 hover:opacity-100"
    />
  </div>
</template>

<script setup lang="ts">
defineProps<{
  image: string;
  hasMultiple: boolean;
  className?: string;
}>();
</script>
