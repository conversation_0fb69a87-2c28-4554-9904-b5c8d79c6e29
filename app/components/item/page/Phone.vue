<template>
  <UButton size="xl" block color="primary" class="flex-col" :loading="isLoading" @click="getPhone">
    <span class="-mb-2 text-lg font-semibold">Показать телефон</span>
    <span>+7 (XXX) XXX-XX-XX</span>
  </UButton>
  <UModal v-model:open="open">
    <template #header>
      {{ post.title }}
    </template>
    <template #body>
      <div class="flex flex-col items-center justify-center gap-4">
        <a
          :href="`tel:${phone?.phone}`"
          class="block w-full text-center font-mono text-4xl font-semibold hover:text-[var(--ui-primary)]/75"
        >
          {{ phone?.phone }}
        </a>
        <p class="text-center">Сообщите продавцу, что вы нашли объявление на gunpost.ru</p>
      </div>
    </template>

    <template #footer>
      <div class="flex w-full items-center justify-between gap-2">
        <UUser
          size="xs"
          target="_blank"
          :name="post.seller_name"
          :avatar="{
            src: post.seller_avatar,
            alt: post.seller_name
          }"
        />
        <UButton
          icon="i-lucide-x"
          variant="soft"
          color="neutral"
          size="xs"
          class="cursor-pointer"
          @click="open = false"
        >
          Закрыть
        </UButton>
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});

const { $metrika } = useNuxtApp();
const client = useSanctumClient();
const open = ref(false);
const isLoading = ref(false);
const phone = ref(null);

const getPhone = async () => {
  isLoading.value = true;

  $metrika.reachGoal("show_phone_button");

  try {
    phone.value = await client(`/posts/${props.post.slug}/phone`, {
      method: "POST"
    });
    open.value = true;
  } catch (e) {
    console.error(e);
  }

  isLoading.value = false;
};
</script>
