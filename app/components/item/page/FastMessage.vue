<template>
  <div>
    <UCard
      variant="subtle"
      class="my-4 mt-auto"
      :ui="{
        header: 'flex items-center gap-1.5 text-(--ui-text-dimmed)',
        body: 'p-3 sm:p-3'
      }"
    >
      <form class="relative">
        <UTextarea
          v-model="message"
          color="neutral"
          variant="none"
          required
          autoresize
          placeholder="Ваше сообщение..."
          :rows="2"
          class="w-full"
          :ui="{ base: 'p-0 pr-4 resize-none' }"
          :disabled="isLoading"
        />

        <div class="absolute top-0 right-0">
          <UButton
            aria-label="Отправить сообщение"
            type="submit"
            color="neutral"
            variant="ghost"
            square
            icon="i-lucide-send"
            :disabled="isLoading"
            @click="sendMessage"
          />
        </div>
      </form>
    </UCard>
    <div class="flex flex-wrap gap-2">
      <UButton
        :disabled="isLoading"
        size="sm"
        color="neutral"
        @click="sendMessage('Еще продаете?')"
      >
        Еще продаете?
      </UButton>
      <UButton
        :disabled="isLoading"
        size="sm"
        color="neutral"
        @click="sendMessage('Торг уместен?')"
      >
        Торг уместен?
      </UButton>
      <UButton
        :disabled="isLoading"
        size="sm"
        color="neutral"
        @click="sendMessage('Когда можно посмотреть?')"
      >
        Когда можно посмотреть?
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});
const { authOpenModal } = useDashboard();
const { isAuthenticated } = useSanctumAuth();
const router = useRouter();
const client = useSanctumClient();
const isLoading = ref(false);

const message = ref("Здравствуйте! ");
const sendMessage = async (msg: string) => {
  if (!isAuthenticated.value) return (authOpenModal.value = true);

  isLoading.value = true;

  try {
    const data = await client(`/posts/${props.post.slug}/message`, {
      method: "POST",
      body: {
        message: msg ?? message.value
      }
    });
    router.push({ path: `/inbox/${data.id}` });
  } catch (e) {
    console.error(e);
  }

  isLoading.value = false;
};
</script>
