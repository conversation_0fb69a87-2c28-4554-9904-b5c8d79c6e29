<template>
  <UButton
    block
    size="xl"
    color="neutral"
    variant="outline"
    icon="i-lucide-mail"
    :loading="isLoading"
    @click="sendMessage"
  >
    Написать сообщение
  </UButton>
</template>

<script setup lang="ts">
const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});

const { $metrika } = useNuxtApp();
const { authOpenModal } = useDashboard();
const { isAuthenticated } = useSanctumAuth();

const router = useRouter();
const client = useSanctumClient();
const isLoading = ref(false);

const sendMessage = async () => {
  if (!isAuthenticated.value) return (authOpenModal.value = true);

  isLoading.value = true;

  $metrika.reachGoal("write_message_button");

  try {
    const data = await client(`/posts/${props.post.slug}/message`, {
      method: "POST"
    });

    router.push({ path: `/inbox/${data.id}` });
  } catch (e) {
    console.error(e);
  }

  isLoading.value = false;
};
</script>
