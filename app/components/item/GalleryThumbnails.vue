<template>
  <div class="hidden gap-2 md:grid" :class="className">
    <template v-for="(image, index) in images" :key="index">
      <div class="relative h-full w-full cursor-pointer">
        <img :src="image" class="h-full w-full object-cover object-center" />
        <div
          class="absolute inset-0 bg-black/30 opacity-0 transition-opacity duration-300 hover:opacity-100"
        />
      </div>
    </template>

    <div v-if="hasMore" class="relative h-full w-full cursor-pointer">
      <img :src="images[images.length - 1]" class="h-full w-full object-cover object-center" />
      <div
        class="absolute inset-0 bg-black/30 opacity-0 transition-opacity duration-300 hover:opacity-100"
      />
      <div class="absolute bottom-2 left-1/2 w-[calc(100%-2rem)] -translate-x-1/2 text-center">
        <UButton
          color="neutral"
          block
          size="sm"
          class="lg:py-2.5"
          variant="subtle"
          icon="i-lucide-grip"
        >
          Показать все фото
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  images: string[];
  hasMore: boolean;
  className?: string;
}>();
</script>
