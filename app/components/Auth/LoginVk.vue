<template>
  <div id="vkid-container" />
</template>

<script setup lang="ts">
useHead({
  script: [
    {
      src: "https://unpkg.com/@vkid/sdk@<3.0.0/dist-sdk/umd/index.js",
      async: true,
      defer: true
    }
  ]
});

onMounted(() => {
  if (import.meta.client && "VKIDSDK" in window) {
    const VKID = window.VKIDSDK;

    VKID.Config.init({
      app: 53261308,
      redirectUrl: "http://localhost/auth",
      responseMode: VKID.ConfigResponseMode.Callback,
      source: VKID.ConfigSource.LOWCODE,
      scope: ""
    });

    const oneTap = new VKID.OneTap();

    oneTap
      .render({
        container: document.getElementById("vkid-container"),
        showAlternativeLogin: true
      })
      .on(VKID.WidgetEvents.ERROR, vkidOnError)
      .on(VKID.OneTapInternalEvents.LOGIN_SUCCESS, function (payload) {
        const { code, device_id: deviceId } = payload;

        VKID.Auth.exchangeCode(code, deviceId).then(vkidOnSuccess).catch(vkidOnError);
      });

    function vkidOnSuccess(data) {
      console.log("VKID Login Success:", data);
      // Обработка полученного результата
    }

    function vkidOnError(error) {
      console.error("VKID Login Error:", error);
      // Обработка ошибки
    }
  }
});
</script>
