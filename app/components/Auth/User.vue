<template>
  <UChip :show="!!user?.notifications || !!user?.messages">
    <UDropdownMenu
      :items="items"
      :content="{
        align: 'start',
        side: 'bottom',
        sideOffset: 8
      }"
      :ui="{
        content: 'w-48'
      }"
    >
      <UButton
        aria-label="Открыть меню"
        label="Open"
        size="md"
        icon="i-lucide-menu"
        color="neutral"
        variant="outline"
      >
        <UAvatar size="2xs" v-bind="user?.avatar" />
      </UButton>
      <template #badge-trailing="{ item }">
        <UBadge v-if="item?.value" size="sm" color="neutral" variant="outline">
          {{ item?.value }}
        </UBadge>
      </template>
    </UDropdownMenu>
  </UChip>
</template>

<script setup lang="ts">
import type { User } from "~/types/auth";

const user = useSanctumUser<User>();
const { logout } = useSanctumAuth<User>();

const items = computed(() => [
  [
    {
      label: "Мои объявления",
      to: "/profile"
    },
    {
      label: "Подать объявление",
      to: "/add"
    },
    {
      label: "Избранное",
      value: user.value?.favorites,
      slot: "badge",
      to: "/favorites"
    }
  ],
  [
    {
      label: "Сообщения",
      value: user.value?.messages,
      slot: "badge",
      to: "/inbox"
    },
    {
      label: "Уведомления",
      value: user.value?.notifications,
      slot: "badge",
      to: "/notifications"
    }
  ],
  [
    // {
    //   label: "Кошелёк",
    //   to: "/wallet",
    //   slot: "badge",
    //   value: user.value.balance
    // },
    {
      label: "Настройки",
      to: "/settings"
    }
  ],
  [
    {
      label: "Выйти",
      onSelect() {
        logout();
      }
    }
  ]
]);
</script>
