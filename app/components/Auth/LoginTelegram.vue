<template>
  <div id="telegram-container" />
</template>

<script setup lang="ts">
// useHead({
//   script: [
//     {
//       src: "https://telegram.org/js/telegram-widget.js?22",
//       "data-telegram-login": "gunpostrubot",
//       "data-size": "small",
//       "data-radius": "4",
//       "data-onauth": "onTelegramAuth(user)",
//       "data-request-access": "write",
//     }
//   ]
// });

function onTelegramAuth(user) {
  alert(
    "Logged in as " +
      user.first_name +
      " " +
      user.last_name +
      " (" +
      user.id +
      (user.username ? ", @" + user.username : "") +
      ")"
  );
}

onMounted(() => {
  if (import.meta.client) {
    window.onTelegramAuth = onTelegramAuth;

    const script = document.createElement("script");
    script.async = true;
    script.src = "https://telegram.org/js/telegram-widget.js?3";

    script.setAttribute("data-telegram-login", "gunpostrubot");
    script.setAttribute("data-size", "small");
    script.setAttribute("data-radius", "4");
    script.setAttribute("data-onauth", "window.onTelegramAuth(user)");

    document.querySelector("#telegram-container").appendChild(script);
  }
});
</script>
