<template>
  <UDropdownMenu
    :items="items"
    :content="{
      align: 'start',
      side: 'bottom',
      sideOffset: 8
    }"
    :ui="{
      content: 'w-48'
    }"
  >
    <UButton
      class="cursor-pointer"
      size="md"
      icon="i-lucide-menu"
      color="neutral"
      variant="outline"
      aria-label="Открыть меню"
    >
      <UAvatar size="2xs" src="/images/avatar.jpg" alt="Войти" />
    </UButton>
  </UDropdownMenu>
</template>

<script setup lang="ts">
const { authOpenModal } = useDashboard();
const items = ref([
  [
    {
      label: "Войти",
      onSelect() {
        authOpenModal.value = true;
      }
    },
    {
      label: "Зарегистрироваться",
      onSelect() {
        authOpenModal.value = true;
      }
    }
  ],
  [
    {
      label: "Подать объявление",
      to: "/add"
    }
  ]
]);
</script>
