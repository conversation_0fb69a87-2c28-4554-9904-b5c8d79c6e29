<template>
  <UPopover v-model:open="open" :handle="false" :content="{ side: 'bottom', align: 'start' }">
    <UButton
      v-if="cityStore?.city?.label"
      variant="ghost"
      color="neutral"
      icon="i-lucide-map-pin"
      square
      :loading="isLoading"
      class="flex items-center"
    >
      <p class="max-w-28 truncate lg:max-w-32">
        {{ cityStore.city.label }}
      </p>
    </UButton>

    <template #content>
      <UCommandPalette
        v-model:search-term="searchTerm"
        placeholder="Выберите регион..."
        :groups="[{ id: 'region', slot: 'region', items: cityStore.cities }]"
        :loading="isLoading"
        :disabled="isLoading"
        @update:model-value="setCity"
      >
        <template #region-leading="{ item }">
          <UBadge variant="subtle" :label="item.region">
            {{ item.region }}
          </UBadge>
        </template>
      </UCommandPalette>
    </template>
  </UPopover>
</template>

<script setup lang="ts">
const route = useRoute();
const router = useRouter();
const cityStore = useCityStore();

const searchTerm = ref("");
const open = ref(false);
const isLoading = ref(false);

const setCity = async (data) => {
  isLoading.value = true;

  await cityStore.setCity(data.value);

  if (route?.params?.city && data.value && route?.params?.city !== data.value) {
    await router.replace({ params: { city: data.value } });
  }

  open.value = false;
  isLoading.value = false;
};
</script>
