<template>
  <div class="mx-auto mt-4 max-w-xs space-y-2">
    <UButton
      v-if="false"
      :disabled="disabled"
      size="xl"
      block
      color="neutral"
      class="cursor-pointer"
      @click.prevent="nextPage"
    >
      Показать еще
    </UButton>
    <UPagination
      v-if="meta?.last_page > 1"
      :ui="{
        list: meta.last_page >= 5 ? 'justify-between w-full' : 'justify-center w-full'
      }"
      :disabled="disabled"
      size="xl"
      color="neutral"
      active-color="neutral"
      show-edges
      :show-controls="false"
      :items-per-page="meta.per_page"
      :sibling-count="1"
      :total="meta.total"
      :page="meta.current_page"
      :to="to"
      @update:page="setPage"
    >
      <template #ellipsis> - </template>
    </UPagination>
  </div>
</template>

<script setup lang="ts">
import type { Meta } from "~/types/meta";

const route = useRoute();

const emits = defineEmits(["setPage", "nextPage"]);
defineProps<{
  meta: Meta;
  disabled: boolean;
}>();

function scrollToTop() {
  const { y } = useScroll(window, { behavior: "smooth" });
  y.value = 0;
}

const setPage = (page: number) => {
  scrollToTop();
  emits("setPage", page);
};

const nextPage = () => {
  emits("nextPage");
};

function to(page: number) {
  return {
    query: {
      ...route.query,
      ...{ page }
    }
  };
}
</script>
