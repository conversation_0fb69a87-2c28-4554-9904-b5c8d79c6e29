<template>
  <UModal
    v-model:open="authOpenModal"
    :dismissible="false"
    :ui="{ content: 'lg:max-w-sm z-11', overlay: 'z-10' }"
    :close="step === 1"
  >
    <template #title>
      <div v-if="step === 1">
        <span class="mb-2"
          >Войдите или зарегистрируйтесь,<br />
          что бы продолжить</span
        >
      </div>
      <span v-if="step === 2"> Звоним по указанному номеру </span>
    </template>

    <template v-if="step === 1" #body>
      <!--            <AuthLoginTelegram class="mb-4"/> -->
      <UForm ref="formPhone" :state="state" class="space-y-4" @submit="sendCode">
        <UFormField size="xl" name="phone" label="Ваш номер телефона">
          <UInput
            v-model="state.phone"
            v-maska="options"
            autofocus
            :autofocus-delay="500"
            class="w-full"
            placeholder="****** 111 22 33"
          />
        </UFormField>

        <UFormField v-if="hasPassword" size="xl" name="password" label="Ваш пароль">
          <UInput
            v-model="state.password"
            class="w-full"
            placeholder="******"
            :type="showPassword ? 'text' : 'password'"
            :ui="{ trailing: 'pe-1' }"
          >
            <template #trailing>
              <UButton
                color="neutral"
                variant="link"
                size="sm"
                :icon="showPassword ? 'i-lucide-eye-off' : 'i-lucide-eye'"
                :aria-label="showPassword ? 'Hide password' : 'Show password'"
                :aria-pressed="showPassword"
                aria-controls="password"
                @click="showPassword = !showPassword"
              />
            </template>
          </UInput>
        </UFormField>

        <UFormField>
          <UCheckbox v-model="hasPassword" label="У меня есть пароль" />
        </UFormField>

        <p class="text-sm text-[var(--ui-text-muted)]">
          Продолжая, вы принимаете
          <a class="text-[var(--ui-text-highlighted)] hover:underline" target="_blank" href="/terms"
            >пользовательское соглашение</a
          >,
          <a
            class="text-[var(--ui-text-highlighted)] hover:underline"
            target="_blank"
            href="/oferta"
            >публичную оферту</a
          >
          и даёте
          <a
            class="text-[var(--ui-text-highlighted)] hover:underline"
            target="_blank"
            href="/privacy_policy"
            >согласие</a
          >
          на обработку персональных данных
        </p>
      </UForm>
    </template>

    <template v-else-if="step === 2" #body>
      <div class="space-y-4">
        <p class="text-[var(--ui-text-muted)]">
          Введите последние 4 цифры номера входящего звонка. Робот произведет звонок на номер
          {{ state.phone }}
          <a class="text-[var(--ui-primary)] hover:underline" href="#" @click="step = 1"
            >Изменить</a
          >
        </p>
        <UForm ref="formPin" :state="state">
          <UFormField name="pin" size="xl">
            <UPinInput
              ref="pinInput"
              v-model="state.pin"
              :ui="{ base: 'text-2xl' }"
              class="w-full"
              otp
              autofocus
              required
              :length="4"
              type="number"
              :disabled="isLoading"
              @complete="verifyCode"
            />
          </UFormField>
        </UForm>
        <div v-if="timer > 0" class="text-sm text-[var(--ui-text-muted)]">
          Запросить звонок повторно можно через
          <span>{{ zeroPad(timerDuration?.minutes) }}:{{ zeroPad(timerDuration?.seconds) }}</span>
        </div>
        <div v-else class="text-sm text-[var(--ui-text-muted)]">
          Запросить код повторно можно
          <a class="text-[var(--ui-primary)] hover:underline" href="#" @click="sendCode">здесь</a>
        </div>
      </div>
    </template>

    <template v-if="step === 1" #footer>
      <UButton
        v-if="hasPassword"
        :loading="isLoading"
        size="xl"
        color="neutral"
        block
        @click="loginViaPassword"
      >
        Войти с паролем
      </UButton>
      <div v-else class="w-full space-y-2">
        <UButton
          block
          size="xl"
          :disabled="isLoading || isTelegramLoading"
          :loading="isLoading"
          color="neutral"
          @click="sendCode"
        >
          Получить код
        </UButton>
        <UButton
          v-if="false"
          block
          size="md"
          :disabled="isLoading || isTelegramLoading"
          :loading="isTelegramLoading"
          color="info"
          @click="sendTelegramCode"
        >
          Получить код в Telegram
        </UButton>
        <UButton v-if="false" size="xl" color="neutral" variant="soft" block @click="step = 1">
          Войти по почте
        </UButton>
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { intervalToDuration } from "date-fns";
import { vMaska } from "maska/vue";
import { StorageSerializers, useStorage } from "@vueuse/core";
import type { MaskInputOptions } from "maska";

const { $metrika } = useNuxtApp();
const { authOpenModal } = useDashboard();
const { refreshIdentity, login } = useSanctumAuth();
const options = reactive<MaskInputOptions>({
  mask: "+7 ### ###-##-##"
});
const cookieCity = useCookie("city");

const consentAdult = useCookie("adult-consent");
const consentCookie = useCookie("cookie-consent");
const client = useSanctumClient();
const formPin = useTemplateRef("formPin");
const formPhone = useTemplateRef("formPhone");
const pinInput = ref(null);
const hasPassword = ref(false);
const showPassword = ref(false);
const step = ref(1);
const timer = ref(30);
const timerDuration = computed(() => intervalToDuration({ start: 0, end: timer.value * 1000 }));
const zeroPad = (num: number) => {
  return num?.toString()?.padStart(2, "0") ?? "00";
};
const isLoading = ref(false);
const isTelegramLoading = ref(false);
const state = reactive({
  phone: "+7",
  password: "",
  pin: []
});
const storagePost = useStorage<{
  uuid: string;
}>("post", null, undefined, { serializer: StorageSerializers.object });

const loginViaPassword = async () => {
  isLoading.value = true;

  $metrika.reachGoal("login_by_password");

  try {
    await login({
      phone: state.phone,
      password: state.password
    });
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      formPhone.value?.setErrors(err.bag);
    }
  }

  isLoading.value = false;
};

const sendTelegramCode = async () => {
  if (!state.phone?.trim() || state.phone?.trim() === "+7") {
    formPhone.value?.setErrors([{ name: "phone", message: "Введите номер телефона" }]);
    return;
  }

  isTelegramLoading.value = true;

  try {
    const data = await client("/send-telegram-code", {
      method: "POST",
      body: {
        phone: state.phone
      }
    });

    step.value = 2;
    timer.value = 30;
    state.phone = data.phone;

    const interval = setInterval(() => {
      if (timer.value === 0) {
        clearInterval(interval);
      } else {
        timer.value--;
      }
    }, 1000);

    useToast().add({
      title: `Дождитесь кода`,
      description: "Код придетв в Telegram на номер " + data.phone,
      color: "success",
      duration: 5000
    });
  } catch (error) {
    state.phone = "+7";
    const err = useSanctumError(error);
    if (err.isValidationError) {
      formPhone.value?.setErrors(err.bag);
    } else {
      formPhone.value?.setErrors([
        { name: "phone", message: "Произошла ошибка, проверьте номер телефона" }
      ]);
    }
  }

  isTelegramLoading.value = false;
};

const sendCode = async () => {
  if (!state.phone || state.phone === "+7") {
    formPhone.value?.setErrors([{ name: "phone", message: "Введите номер телефона" }]);
    return;
  }
  isLoading.value = true;
  $metrika.reachGoal("get_sms_code");

  try {
    const data = await client("/send-code", {
      method: "POST",
      body: {
        phone: state.phone
      }
    });

    step.value = 2;
    timer.value = 30;
    state.phone = data.phone;

    const interval = setInterval(() => {
      if (timer.value === 0) {
        clearInterval(interval);
      } else {
        timer.value--;
      }
    }, 1000);

    useToast().add({
      title: data.type === "phone" ? `Дождитесь звонка` : `Дождитесь сообщения`,
      description:
        data.type === "phone"
          ? "Звонок поступит на номер " + data.phone
          : "Сообщение придет на номер в привязанный telegram",
      color: "success",
      duration: 5000
    });
  } catch (error) {
    state.phone = "+7";
    const err = useSanctumError(error);
    if (err.isValidationError) {
      formPhone.value?.setErrors(err.bag);
    } else {
      formPhone.value?.setErrors([
        { name: "phone", message: "Произошла ошибка, проверьте номер телефона" }
      ]);
    }
  }

  isLoading.value = false;
};

const verifyCode = async () => {
  isLoading.value = true;

  try {
    await client("/verify-code", {
      method: "POST",
      body: {
        phone: state.phone,
        code: state.pin.join(""),
        city_id: cookieCity.value,
        consents: {
          "law-consent": consentCookie.value,
          "adult-consent": consentAdult.value,
          "cookie-consent": consentCookie.value
        },
        uuid: storagePost?.value?.uuid
      }
    });
    await refreshIdentity();
    useToast().add({
      title: "Вы успешно вошли в аккаунт",
      color: "success"
    });
  } catch (error) {
    state.pin = [];
    const err = useSanctumError(error);
    if (err.isValidationError) {
      formPin.value?.setErrors(err.bag);
    }
  }

  state.pin = [];
  isLoading.value = false;
};
</script>
