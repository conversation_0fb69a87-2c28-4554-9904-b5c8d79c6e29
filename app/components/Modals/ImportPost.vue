<template>
  <UModal v-model:open="importModal" title="Импорт ваших объявлений">
    <UButton icon="i-lucide-import" color="neutral" size="xl" block @click="importModal = true">
      Импортировать мои объявления с других сайтов
    </UButton>

    <template #body>
      <UAlert
        color="info"
        variant="subtle"
        title="Импортируются объявления только при совпадении номера телефона"
        description="Если ваш номер телефона не совпадает с номером телефона на gunsbroker, то объявления не будут импортированы."
      />

      <UForm ref="form" :state="state" class="mt-4 space-y-4" @submit="getByLink">
        <UFormField
          name="link"
          required
          size="xl"
          label="Ссылка на объявление"
          description="Укажите ссылку на объявление"
        >
          <UInput
            v-model="state.link"
            class="w-full"
            placeholder="Например: ...broker.ru/ob-avtomaticheskom-ak-74.html"
          />
        </UFormField>

        <p>
          Нажимая «Импортировать объявление», я подтверждаю свои права на текст и фото объявления на
          другом сайте и безвозмездно разрешаю gunpost.ru их копировать, хранить и публиковать
        </p>

        <UButton :loading="isDataLoading" type="submit" size="xl" icon="i-lucide-import" block>
          Импортировать объявление
        </UButton>
      </UForm>

      <UModal v-model:open="postModal" title="Second modal" :ui="{ footer: 'justify-end' }">
        <template #title>
          {{ postData.title }}
        </template>
        <template #body>
          <div class="space-y-2">
            <div v-if="postData?.photos?.length" class="flex flex-wrap gap-2">
              <div v-for="photo in postData.photos" :key="photo.filename">
                <img :src="photo.url" alt="gunpost.ru" class="h-24 w-24 object-cover" />
              </div>
            </div>
            <ul class="mt-4 space-y-2 border-t border-[var(--ui-border)] pt-4">
              <li class="flex flex-wrap items-center justify-between">
                <span>Торг</span>
                <span v-if="postData?.is_rebate" class="flex items-center gap-2 font-semibold">
                  Возможен
                  <UIcon name="i-lucide-check" class="text-green-500" />
                </span>
                <span v-else class="flex items-center gap-2 font-semibold">
                  Нет
                  <UIcon name="i-lucide-x" class="text-warning-500" />
                </span>
              </li>
              <li class="flex flex-wrap items-center justify-between">
                <span>Обмен</span>
                <span v-if="postData?.is_trade" class="flex items-center gap-2 font-semibold">
                  Возможен
                  <UIcon name="i-lucide-check" class="text-green-500" />
                </span>
                <span v-else class="flex items-center gap-2 font-semibold">
                  Нет
                  <UIcon name="i-lucide-x" class="text-warning-500" />
                </span>
              </li>
              <li class="flex flex-wrap items-center justify-between">
                <span>Стоимость</span>
                {{ $currency(postData.price) }}
              </li>
              <li
                v-for="(attributeValue, attribute) in postData?.source_attributes"
                :key="attribute"
                class="flex flex-wrap items-center justify-between"
              >
                <span>{{ attribute }}</span>
                {{ attributeValue }}
              </li>
            </ul>
            <div class="border-t border-[var(--ui-border)] pt-4 whitespace-pre-line">
              {{ postData.description }}
            </div>
          </div>
        </template>
        <template #footer>
          <UButton label="Отмена" color="neutral" variant="outline" @click="cancelImport" />
          <UButton :loading="isDataImporting" color="success" @click="importPost"
            >Да, импортировать</UButton
          >
        </template>
      </UModal>
    </template>
  </UModal>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const { $currency } = useNuxtApp();
const form = useTemplateRef("form");
const postModal = ref(false);
const importModal = ref(false);
const isDataLoading = ref(false);
const isDataImporting = ref(false);
const state = reactive({
  link: ""
});
const postData = ref(null);
const emits = defineEmits(["refresh"]);

async function getByLink() {
  isDataLoading.value = true;

  try {
    const { data } = await client("/import", {
      method: "POST",
      body: {
        link: state.link
      }
    });
    postData.value = data;
    postModal.value = true;
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      form.value?.setErrors(err.bag);
    }
  }

  emits("refresh");
  isDataLoading.value = false;
}

async function importPost() {
  isDataImporting.value = true;

  try {
    await client("/import/save", {
      method: "POST",
      body: {
        link: state.link
      }
    });
    postModal.value = false;
    importModal.value = false;
    state.link = "";
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      form.value?.setErrors(err.bag);
    }
  }

  emits("refresh");
  isDataImporting.value = false;
}

function cancelImport() {
  postModal.value = false;
  importModal.value = false;
  state.link = "";

  emits("refresh");
}
</script>
