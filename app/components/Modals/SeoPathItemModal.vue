<template>
  <UModal v-model:open="open" title="Редактировать">
    <UButton>
      {{ localItem?.path ? "Редактировать" : "Создать SEO страницу" }}
    </UButton>

    <template #body>
      <UForm ref="form" :state="localItem" class="space-y-2" @submit="saveOrCreate">
        <UFormField required size="xl" label="Путь страницы" name="path">
          <UInput
            v-model="localItem.path"
            class="w-full"
            placeholder="Начинается с /"
            :disabled="!!item?.id"
          />
        </UFormField>
        <UFormField required size="xl" label="Page заголовок" name="title">
          <UInput v-model="localItem.title" class="w-full" />
        </UFormField>
        <UFormField required size="xl" label="Page описание" name="meta_description">
          <UInput v-model="localItem.meta_description" class="w-full" />
        </UFormField>
        <UFormField size="xl" label="Page ключевые слова" name="meta_keywords">
          <UInput v-model="localItem.meta_keywords" class="w-full" />
        </UFormField>
        <UFormField required size="xl" label="H1 Заголовок" name="h1">
          <UInput v-model="localItem.h1" class="w-full" />
        </UFormField>
        <UFormField size="xl" label="Основной контент" name="content">
          <UTextarea v-model="localItem.content" autoresize class="w-full" />
        </UFormField>

        <UButton :loading="isLoading" type="submit">
          {{ localItem?.path ? "Сохранить" : "Создать SEO страницу" }}
        </UButton>
      </UForm>
    </template>
  </UModal>
</template>

<script setup lang="ts">
interface ItemSeoPath {
  id?: number;
  path: string;
  title: string;
  meta_description: string;
  meta_keywords: string;
  h1: string;
  content: string;
}
const props = defineProps<{
  item?: ItemSeoPath;
}>();
const toast = useToast();
const open = ref(false);
const form = useTemplateRef("form");
const client = useSanctumClient();
const isLoading = ref(false);
const emit = defineEmits(["refresh"]);

const localItem = ref({ ...(props.item || {}) });
watch(
  () => props.item,
  (val) => {
    localItem.value = { ...val };
  }
);

const saveOrCreate = async () => {
  isLoading.value = true;

  try {
    await client(`/moderation/seo/${localItem.value?.id ?? "create"}`, {
      method: "POST",
      body: localItem.value
    });

    emit("refresh");
    open.value = false;
    toast.add({
      title: "SEO страница успешно сохранена",
      color: "success"
    });
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      form.value?.setErrors(err.bag);
    }
  }

  isLoading.value = false;
};
</script>
