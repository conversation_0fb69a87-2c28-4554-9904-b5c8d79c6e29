<template>
  <UModal
    v-model:open="open"
    title="Снять с публикации"
    description="Если объявление более неактуально, то необходимо его снять с публикации"
  >
    <template #body>
      <div class="space-y-4">
        <UFormField label="Выбирите причину" name="reason">
          <URadioGroup v-model="reason" class="mt-2" variant="table" :items="items" />
        </UFormField>

        <UFormField
          v-if="reason === 3"
          label="Укажите пожалуйста комментарий"
          name="reason_comment"
        >
          <UInput v-model="reason_comment" size="xl" class="w-full" />
        </UFormField>
      </div>
    </template>
    <template v-if="reason >= 1" #footer>
      <UButton color="neutral" :loading="loading" @click="emits('set', { reason, reason_comment })">
        Снять с публикации
      </UButton>
      <UButton color="neutral" variant="link" @click="emits('close')"> Отмена </UButton>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import type { RadioGroupItem, RadioGroupValue } from "@nuxt/ui";

const open = ref(true);
defineProps<{
  loading: boolean;
}>();
const emits = defineEmits(["set", "close"]);
const items = ref<RadioGroupItem[]>([
  {
    label: "Продал на Ганпост",
    // description: 'This is the first option.',
    value: 1
  },
  {
    label: "Продал где-то еще",
    // description: 'This is the second option.',
    value: 2
  },
  {
    label: "Другая причина",
    // description: 'This is the third option.',
    value: 3
  }
]);
const reason = ref<RadioGroupValue>("system");
const reason_comment = ref("");
</script>
