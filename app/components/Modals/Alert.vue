<script setup lang="ts">
defineProps<{
  title?: string;
  description?: string;
  actions?: {
    label: string;
    color: string;
    icon?: string;
    variant?: string;
    onClick: () => void;
  }[];
}>();
const emit = defineEmits<{ close: [boolean] }>();
function clickTo(fun: () => void) {
  emit("close", false);
  fun();
}
</script>

<template>
  <UModal :close="{ onClick: () => emit('close', false) }">
    <template v-if="title" #title>
      {{ title }}
    </template>
    <template #description>
      {{ description }}
    </template>
    <template #footer>
      <div class="flex gap-2">
        <UButton label="Отмена" color="neutral" variant="outline" @click="emit('close', false)" />
        <UButton
          v-for="action in actions"
          :key="action.label"
          :label="action.label"
          :icon="action?.icon"
          :color="action?.color ?? 'primary'"
          :variant="action?.variant ?? 'solid'"
          @click="clickTo(action.onClick)"
        />
      </div>
    </template>
  </UModal>
</template>
