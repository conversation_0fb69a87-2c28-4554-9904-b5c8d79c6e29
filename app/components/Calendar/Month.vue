<template>
  <div class="lg:flex lg:h-full lg:flex-col">
    <header
      class="flex items-center justify-between border-b border-gray-200 px-6 py-4 lg:flex-none"
    >
      <h1 class="text-base leading-6 font-semibold text-gray-900">
        <time :datetime="format(currentMonth, 'yyyy-MM')">{{
          format(currentMonth, "MMMM yyyy", { locale: ru })
        }}</time>
      </h1>
    </header>
    <div class="rounded-md shadow ring-(--ui-border-accented)/50 lg:flex lg:flex-auto lg:flex-col">
      <div class="flex items-center justify-between px-6 py-4">
        <UButton icon="i-lucide-chevron-left" square @click="$emit('prevMonth')" />
        <h2 class="text-sm font-semibold text-gray-900">
          {{ format(currentMonth, "MMMM yyyy", { locale: ru }) }}
        </h2>
        <UButton icon="i-lucide-chevron-right" square @click="$emit('nextMonth')" />
      </div>
      <div
        class="grid grid-cols-7 gap-px border-b text-center text-xs leading-6 font-semibold lg:flex-none"
      >
        <div
          v-for="day in ['пн', 'вт', 'ср', 'чт', 'пт', 'сб', 'вс']"
          :key="day"
          class="flex justify-center bg-white py-2"
        >
          <span>{{ day }}</span>
        </div>
      </div>
      <div class="flex text-xs leading-6 lg:flex-auto">
        <div class="hidden w-full lg:grid lg:grid-cols-7 lg:grid-rows-6 lg:gap-px">
          <div v-for="(week, weekIndex) in weeks" :key="weekIndex" class="contents">
            <div
              v-for="day in week"
              :key="day.date.toString()"
              :class="[
                'relative min-h-20 p-3',
                day.isCurrentMonth ? 'bg-white' : 'bg-gray-50 text-gray-500',
                day.isToday && 'font-semibold',
                'hover:bg-gray-100'
              ]"
            >
              <time
                :datetime="format(day.date, 'yyyy-MM-dd')"
                :class="[
                  day.isToday &&
                    'flex h-6 w-6 items-center justify-center rounded-full bg-indigo-600 text-white'
                ]"
              >
                {{ format(day.date, "d") }}
              </time>
              <!-- В десктопной версии показываем список событий -->
              <ol class="mt-2 space-y-2">
                <li
                  v-for="event in day.events.slice(0, 2)"
                  :key="event.id"
                  class="group flex items-start"
                >
                  <NuxtLink
                    :to="{ name: 'events-slug', params: { slug: event.slug } }"
                    target="_blank"
                    class="min-w-0 flex-1"
                  >
                    <p
                      class="line-clamp-2 text-xs font-medium text-gray-900 group-hover:text-indigo-600"
                    >
                      {{ event.title }}
                    </p>
                    <p v-if="event.place" class="truncate text-xs text-gray-500">
                      {{ event.place }}
                    </p>
                  </NuxtLink>
                </li>
                <li v-if="day.events.length > 2" class="mt-1 text-xs text-gray-500">
                  +{{ day.events.length - 2 }} еще
                </li>
              </ol>
            </div>
          </div>
        </div>
        <div class="isolate grid w-full grid-cols-7 grid-rows-6 gap-px lg:hidden">
          <template v-for="(week, weekIndex) in weeks" :key="'mobile-week-' + weekIndex">
            <button
              v-for="day in week"
              :key="'mobile-day-' + day.date.toString()"
              type="button"
              :class="[
                'flex h-14 flex-col px-3 py-2 focus:z-10',
                day.isCurrentMonth ? 'bg-white' : 'bg-gray-50',
                day.isToday ? 'font-semibold' : '',
                'hover:bg-gray-100',
                day.isToday
                  ? 'text-indigo-600'
                  : day.isCurrentMonth
                    ? 'text-gray-900'
                    : 'text-gray-400'
              ]"
            >
              <time
                :datetime="format(day.date, 'yyyy-MM-dd')"
                :class="[
                  'ml-auto flex h-6 w-6 items-center justify-center rounded-full',
                  day.isToday ? 'bg-indigo-600 text-white' : ''
                ]"
              >
                {{ format(day.date, "d") }}
              </time>

              <!-- Отображение событий -->
              <div v-if="day.events.length > 0" class="mt-2">
                <!-- В мобильной версии показываем только точки -->
                <div class="-mx-0.5 flex flex-wrap-reverse justify-center lg:hidden">
                  <span
                    v-for="(event, index) in day.events.slice(0, 3)"
                    :key="index"
                    class="mx-0.5 mb-1 h-1.5 w-1.5 rounded-full bg-indigo-500"
                  ></span>
                  <span v-if="day.events.length > 3" class="text-xs text-gray-500"
                    >+{{ day.events.length - 3 }}</span
                  >
                </div>
              </div>
              <p v-else class="sr-only">Нет событий</p>
            </button>
          </template>
        </div>
      </div>
    </div>

    <div class="mt-6">
      <div v-for="(week, weekIndex) in weeks" :key="weekIndex" class="contents">
        <div v-for="day in week" :key="'mobile-day-' + day.date.toString()">
          <div class="mt-2">
            <UChangelogVersions>
              <UChangelogVersion
                v-for="event in day.events"
                :key="event.slug"
                v-bind="event"
                :to="{ name: 'events-slug', params: { slug: event.slug } }"
              />
            </UChangelogVersions>
          </div>
        </div>
      </div>
      <!--      <UChangelogVersions> -->
      <!--        <UChangelogVersion -->
      <!--          v-for="event in events" -->
      <!--          :key="event.slug" -->
      <!--          v-bind="event" -->
      <!--          :to="{ name: 'events-slug', params: { slug: event.slug } }" -->
      <!--        /> -->
      <!--      </UChangelogVersions> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  startOfMonth,
  endOfMonth,
  format,
  addMonths,
  subMonths,
  eachDayOfInterval,
  isSameMonth,
  isToday,
  isSameDay,
  isWithinInterval,
  parseISO,
  startOfWeek,
  endOfWeek
} from "date-fns";
import { ru } from "date-fns/locale";

interface Event {
  id: string;
  title: string;
  start_date: string;
  end_date: string;
  place: string;
  cover: string;
  [key: string]: any;
}

const props = defineProps<{
  events?: Event[];
  weeks: object;
  currentMonth: object;
}>();
</script>
