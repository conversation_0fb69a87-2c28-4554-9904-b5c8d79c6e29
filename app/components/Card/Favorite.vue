<template>
  <UButton
    block
    size="sm"
    :loading="isLoading"
    :variant="is_favorite ? 'subtle' : 'outline'"
    :color="is_favorite ? 'error' : 'neutral'"
    icon="i-lucide-heart"
    @click="toggleFavorite"
  >
    {{ is_favorite ? "Сохранено" : "Сохранить" }}
  </UButton>
</template>

<script setup lang="ts">
import type { Post } from "~/types/post";

const { authOpenModal } = useDashboard();
const { isAuthenticated } = useSanctumAuth();
const client = useSanctumClient();
const toast = useToast();

interface Props {
  post: Post;
  is_favorite?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  is_favorite: false
});

const { $metrika } = useNuxtApp();
const isLoading = ref(false);
const emits = defineEmits(["toggle"]);

const toggleFavorite = async (): Promise<void> => {
  if (!isAuthenticated.value) {
    authOpenModal.value = true;
    return;
  }
  isLoading.value = true;

  $metrika.reachGoal("add_favorite");

  try {
    await client(`/posts/${props.post.slug}/favorite`, {
      method: "POST"
    });

    if (!props.is_favorite) {
      toast.add({
        title: "Вы успешно добавили в избранное",
        color: "success",
        actions: [
          {
            label: "Перейти в избранное",
            color: "neutral",
            to: "/favorites"
          }
        ]
      });
    }

    emits("toggle", !props.is_favorite);
  } catch (e) {
    console.error(e);
  }

  isLoading.value = false;
};
</script>
