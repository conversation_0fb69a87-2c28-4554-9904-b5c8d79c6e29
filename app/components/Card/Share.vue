<template>
  <UButton
    block
    size="sm"
    variant="outline"
    color="neutral"
    icon="i-lucide-share"
    @click="startShare"
  >
    Поделиться
  </UButton>
</template>

<script setup lang="ts">
import { useShare } from "@vueuse/core";
import type { Post } from "~/types/post";

const props = defineProps<{
  post: Post;
}>();
const { share } = useShare();

const startShare = () => {
  share({
    title: `Посмотри объявление "${props.post.title}" на GunPost:\n`,
    url: location.href
  });
};
</script>
