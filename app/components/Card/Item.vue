<template>
  <NuxtLink
    :to="{ name: 'post', params: { slug: item.slug, category: item.category } }"
    class="flex w-full flex-col"
    :class="{
      'rounded-md bg-yellow-50 dark:bg-yellow-950': hasColorPromo
    }"
  >
    <!-- Галерея изображений -->
    <div>
      <CardGallery
        :fetchpriority="position === 0 ? 'high' : 'low'"
        :images="item.thumbnails"
        :large="!!hasVipPromo"
        :class-name="hasVipPromo ? 'h-48 md:h-24 w-full' : 'h-64 md:h-48 w-full'"
        :alt="item.title"
      />
    </div>

    <!-- Контент карточки -->
    <div
      class="flex flex-1 flex-col gap-2 pt-2"
      :class="{
        'p-3': hasColorPromo
      }"
    >
      <!-- Заголовок -->
      <h3
        class="line-clamp-2 text-sm font-semibold md:text-base"
        :class="{ 'text-yellow-950 dark:text-yellow-100': hasColorPromo }"
      >
        {{ item.title }}
      </h3>

      <!-- Описание -->
      <p class="line-clamp-3 text-xs text-[var(--ui-text-muted)] md:text-sm">
        {{ item.description_short }}
      </p>

      <!-- Метаинформация -->
      <div class="mt-auto space-y-2 pt-2">
        <!-- Локация -->
        <div
          v-if="item?.address ?? item?.city_name"
          class="flex items-center gap-1 text-xs text-[var(--ui-text-muted)]"
        >
          <UIcon name="i-lucide-map-pin" class="size-3" />
          <span class="line-clamp-1">{{ item.address ?? item.city_name }}</span>
        </div>

        <!-- Дата публикации -->
        <div
          v-if="item?.published_at"
          class="flex items-center gap-1 text-xs text-[var(--ui-text-muted)]"
        >
          <UIcon name="i-lucide-clock" class="size-3" />
          <NuxtTime :datetime="item.published_at" date-style="medium" time-style="short" />
        </div>
      </div>

      <!-- Цена -->
      <div class="border-t border-gray-100 pt-3 dark:border-gray-800">
        <span class="text-sm font-bold md:text-base">
          {{ $currency(item.price) }}
        </span>
      </div>
    </div>
  </NuxtLink>
</template>

<script setup lang="ts">
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  small: {
    type: Boolean,
    default: false
  },
  position: {
    type: Number,
    default: 0
  }
});

const { $currency } = useNuxtApp();

const hasColorPromo = computed(() => props.item?.promotion?.["color"]);
const hasVipPromo = computed(() => props.item?.promotion?.["vip"]);
</script>
