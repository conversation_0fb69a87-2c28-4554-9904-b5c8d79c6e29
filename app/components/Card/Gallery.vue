<template>
  <div class="space-y-3 overflow-hidden">
    <!-- Основное изображение -->
    <NuxtImg
      v-if="!hovered"
      class="w-full rounded-md object-cover"
      :class="className"
      :src="cover"
      :alt="alt"
      :fetchpriority="fetchpriority"
      :loading="fetchpriority === 'high' ? 'eager' : 'lazy'"
      @mouseover="handleMouseover"
    />

    <!-- Карусель при наведении -->
    <UCarousel
      v-else
      v-slot="{ item }"
      dots
      loop
      :autoplay="{ delay: 1000 }"
      fade
      :items="images"
      :ui="{
        dots: 'bottom-3',
        dot: 'size-1.5 mx-1',
        container: 'rounded-md overflow-hidden'
      }"
      class="w-full"
      @mouseleave="hovered = false"
    >
      <NuxtImg
        :src="item"
        :fetchpriority="fetchpriority"
        :loading="fetchpriority === 'high' ? 'eager' : 'lazy'"
        :alt="alt"
        class="w-full rounded-md object-cover"
        :class="className"
      />
    </UCarousel>

    <!-- Дополнительные изображения для VIP -->
    <div v-if="large && hasAdditionalImages" class="mt-3 grid grid-cols-2 gap-3">
      <NuxtImg
        v-if="additionalImage1"
        class="w-full rounded-md object-cover"
        :class="[className, onlyOneAdditional ? 'col-span-2' : '']"
        :src="additionalImage1"
        :alt="`${alt} - изображение 2`"
        :fetchpriority="fetchpriority"
        :loading="fetchpriority === 'high' ? 'eager' : 'lazy'"
        @mouseover="handleMouseover"
      />
      <NuxtImg
        v-if="additionalImage2"
        class="w-full rounded-md object-cover"
        :class="className"
        :src="additionalImage2"
        :alt="`${alt} - изображение 3`"
        :fetchpriority="fetchpriority"
        :loading="fetchpriority === 'high' ? 'eager' : 'lazy'"
        @mouseover="handleMouseover"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  alt: {
    type: String,
    default: "gunpost.ru"
  },
  className: {
    type: String,
    default: "h-48 w-full"
  },
  images: {
    type: Array as () => string[],
    default: () => []
  },
  large: {
    type: Boolean,
    default: false
  },
  fetchpriority: {
    type: String as () => "high" | "low" | "auto",
    default: "low"
  }
});

// Состояние
const hovered = ref(false);

const onlyOneAdditional = computed(
  () => props.large && !!additionalImage1.value && !additionalImage2.value
);

// Вычисляемые свойства
const cover = computed(() => props.images[0] || "/images/post.jpg");
const additionalImage1 = computed(() => (props.large ? props.images[1] || null : null));
const additionalImage2 = computed(() => (props.large ? props.images[2] || null : null));
const hasAdditionalImages = computed(
  () => props.large && (additionalImage1.value !== null || additionalImage2.value !== null)
);

// Методы
const handleMouseover = () => {
  if (props.images.length > 1) {
    hovered.value = true;
  }
};
</script>
