<template>
  <div class="flex w-full gap-2">
    <NuxtLink :to="item.to">
      <UAvatar v-bind="item.avatar" size="xl" />
    </NuxtLink>
    <div class="flex w-full flex-col">
      <div class="px-0.5">
        <div class="flex gap-1">
          <UBadge v-if="item.price" variant="outline" size="sm">{{ $currency(item.price) }}</UBadge>
          <NuxtLink :to="item.to" class="text-lg font-medium md:line-clamp-1 md:text-sm">
            {{ item.label }}
          </NuxtLink>
        </div>
        <p
          v-if="item.description_short"
          class="text-md mb-auto text-[var(--ui-text-muted)] md:line-clamp-2 md:text-sm"
        >
          {{ item.description_short }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  item: {
    type: Object,
    required: true
  }
});
const { $currency } = useNuxtApp();
</script>
