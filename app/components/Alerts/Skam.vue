<template>
  <div class="text-sm">
    <div
      class="relative flex w-full items-start gap-2.5 overflow-hidden rounded-[calc(var(--ui-radius)*2)] p-4 ring ring-(--ui-error)/50 ring-inset"
    >
      <UIcon name="i-lucide-badge-alert" class="size-7 text-red-800" />
      <div class="flex min-w-0 flex-1 flex-col">
        <div class="text-sm font-medium">Уважаемые пользователи!</div>
        <div class="mt-1 text-sm opacity-90">
          К сожалению, никто не застрахован от встречи с мошенником. Пожалуйста,
          <a class="underline" href="/obman">ознакомьтесь с данной статьей</a>
          и будьте бдительны. Если Вы столкнулись с мошенничеством на нашем сайте,
          <span class="cursor-pointer underline" @click="toMessage">напишите нам</span>
          и обратитесь в правоохранительные органы. Мы предоставим необходимые данные по
          официальному запросу.
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from "~/types/auth";

const user = useSanctumUser<User>();
const { isAuthenticated } = useSanctumAuth();
const { authOpenModal } = useDashboard();
const toMessage = () => {
  if (isAuthenticated.value) {
    navigateTo({
      path: `/inbox/${user.value?.support_chat_id}`
    });
  }
  authOpenModal.value = true;
};
</script>
