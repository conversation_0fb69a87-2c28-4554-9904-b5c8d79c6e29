<template>
  <UModal v-model:open="isSearchOpen">
    <UButton
      icon="i-lucide-search"
      color="neutral"
      variant="outline"
      block
      class="bg-(--ui-bg-elevated)/25 ring-(--ui-border-accented)/50"
      aria-label="Быстрый поиск"
    >
      <span class="hidden max-w-32 truncate lg:block lg:max-w-none"
        >Быстрый поиск, например: Иж-27</span
      >
      <div class="ms-auto hidden items-center gap-0.5 sm:flex">
        <UKbd value="meta" variant="subtle" />
        <UKbd value="K" variant="subtle" />
      </div>
    </UButton>

    <template #content>
      <UCommandPalette
        v-model:search-term="searchTerm"
        :loading="isLoading"
        :groups="groups"
        placeholder="Быстрый поиск, например: Иж-27..."
        class="h-96"
      >
        <template #empty>
          <div v-if="!searchTerm" />
          <div v-else-if="!isLoading" class="flex items-center justify-center gap-1.5">
            <UIcon name="i-lucide-search-x" class="size-4" />
            <span>Ничего не найдено, попробуйте другой запрос</span>
          </div>
          <div v-else-if="isLoading" class="flex items-center justify-center gap-1.5">
            <span>Идет поиск...</span>
          </div>
        </template>
      </UCommandPalette>
    </template>
  </UModal>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const { isSearchOpen } = useDashboard();
const cityStore = useCityStore();
const isLoading = ref(false);
const searchTerm = ref("");
const guns = ref([]);

const search = async () => {
  isLoading.value = true;
  guns.value = await client(`/search`, {
    params: {
      q: searchTerm.value,
      city_slug: cityStore.routeSlug
    }
  });

  isLoading.value = false;
};

watch(searchTerm, async () => {
  await search();
});

watch(isSearchOpen, async () => {
  if (isSearchOpen.value) {
    await search();
  }
});

const groups = computed(() => [
  {
    id: "guns",
    label: searchTerm.value ? `Поиск “${searchTerm.value}”...` : "Популярные запросы",
    items: guns.value?.data || [],
    ignoreFilter: true
  }
]);
</script>
