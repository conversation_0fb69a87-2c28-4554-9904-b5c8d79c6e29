<template>
  <UButtonGroup>
    <UButton
      v-if="isFiltered"
      icon="i-lucide-x"
      square
      color="neutral"
      variant="soft"
      @click.prevent="resetPrice"
    />
    <UPopover
      v-model:open="open"
      :content="{
        align: 'start',
        side: 'bottom',
        sideOffset: 8,
        class: 'p-0'
      }"
    >
      <UButton
        :variant="isFiltered ? 'subtle' : 'outline'"
        trailing-icon="i-lucide-chevron-down"
        :label="priceString"
        color="neutral"
        @click.prevent="open = true"
      />
      <template #content>
        <UForm :state="state" class="space-y-3 p-3" @submit="setPrice">
          <UFormField label="Цена, ₽">
            <UButtonGroup class="max-w-80">
              <UInput v-model="state.min_price" type="number" placeholder="От" />
              <UInput v-model="state.max_price" type="number" placeholder="До" />
            </UButtonGroup>
          </UFormField>
          <UButton block type="submit" label="Применить" />
        </UForm>
      </template>
    </UPopover>
  </UButtonGroup>
</template>

<script setup lang="ts">
const route = useRoute();
const router = useRouter();
const { $currency } = useNuxtApp();

const open = ref(false);
const state = reactive({
  min_price: route.query?.min_price,
  max_price: route.query?.max_price
});

const isFiltered = computed(() => route.query?.min_price || route.query?.max_price);

const priceString = computed(() => {
  const min = route.query?.min_price;
  const max = route.query?.max_price;
  if (min && max) return `${$currency(min)}–${$currency(max)}`;
  if (min) return `От ${$currency(min)}`;
  if (max) return `До ${$currency(max)}`;
  return "Стоимость";
});

const setPrice = async () => {
  if (state.min_price && state.max_price && state.min_price > state.max_price) {
    // Если min_price больше max_price, меняем их местами
    const temp = state.min_price;
    state.min_price = state.max_price;
    state.max_price = temp;
  }

  open.value = false;
  await router.replace({
    query: {
      ...route.query,
      min_price: state.min_price ?? undefined,
      max_price: state.max_price ?? undefined
    }
  });
};
const resetPrice = async () => {
  state.min_price = undefined;
  state.max_price = undefined;
  await router.replace({
    query: {
      ...route.query,
      ...{ min_price: undefined, max_price: undefined }
    }
  });
};
</script>
