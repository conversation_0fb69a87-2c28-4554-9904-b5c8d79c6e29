<template>
  <UPopover v-model:open="open" :content="{ side: 'right', align: 'start', class: 'p-0' }">
    <UButton
      variant="outline"
      color="neutral"
      icon="i-lucide-map-pin"
      class="flex items-center gap-2"
    >
      <p class="max-w-32 truncate text-sm lg:max-w-32">
        {{ getCity }}
      </p>
    </UButton>

    <template #content>
      <UCommandPalette
        v-model:search-term="searchTerm"
        class="w-80 md:w-96"
        placeholder="Выберите регион..."
        :groups="[{ id: 'region', slot: 'region', items: cityStore.cities }]"
        @update:model-value="onSelect"
      >
        <template #region-leading="{ item }">
          <UBadge variant="subtle" :label="item.region">
            {{ item.region }}
          </UBadge>
        </template>
      </UCommandPalette>
    </template>
  </UPopover>
</template>

<script setup lang="ts">
const route = useRoute();
const cityStore = useCityStore();

const searchTerm = ref("");
const open = ref(false);

const getCity = computed(() => {
  if (route?.params?.city) {
    return cityStore.cities.find((city) => city.value === route?.params?.city)?.label;
  }

  return "Вся Россия";
});

const onSelect = async (data) => {
  await navigateTo({
    name: "city-category",
    params: { ...route.params, ...{ city: data.value } },
    query: route.query
  });
  await cityStore.setCity(data.value);
};
</script>
