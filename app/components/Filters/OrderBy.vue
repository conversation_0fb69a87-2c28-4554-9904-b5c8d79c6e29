<template>
  <UButtonGroup class="w-full md:w-auto">
    <USelect
      v-model="selectedOrder"
      class="w-full md:w-auto"
      color="neutral"
      variant="soft"
      aria-label="Сортировка"
      :items="['По популярности', 'По дате', 'По цене']"
      @change="updateSort"
    />
    <UButton
      v-if="selectedOrder !== 'По популярности'"
      :trailing-icon="orderType === '-1' ? 'i-lucide-arrow-down-1-0' : 'i-lucide-arrow-up-1-0'"
      color="neutral"
      variant="soft"
      aria-label="Сортировка"
      @click="toggleOrderType"
    />
  </UButtonGroup>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();

// Состояние для выбранного параметра сортировки
const selectedOrder = ref("По популярности");

// Состояние для типа сортировки (по убыванию или возрастанию)
const orderType = ref("-1"); // -1 — это убывание, 1 — возрастание

// Функция для обновления параметров сортировки в URL
const updateSort = () => {
  const order =
    selectedOrder.value === "По популярности"
      ? null
      : selectedOrder.value === "По дате"
        ? "date"
        : "price";
  router.replace({
    query: {
      ...route.query,
      order: order,
      order_type: orderType.value
    }
  });
};

// Функция для переключения типа сортировки (убывание/возрастание)
const toggleOrderType = () => {
  orderType.value = orderType.value === "-1" ? "1" : "-1";
  updateSort(); // Обновляем сортировку после переключения типа
};

// Инициализация параметров из текущего URL
if (route.query.order) {
  selectedOrder.value = route.query.order === "date" ? "По дате" : "По цене";
}
if (route.query.order_type) {
  orderType.value = route.query.order_type;
}
</script>
