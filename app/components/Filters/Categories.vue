<template>
  <div class="w-full">
    <!-- Мобильная карусель -->
    <UCarousel
      v-if="$device?.isMobile"
      :items="slides"
      align="end"
      class="animate-peek w-full"
      :ui="{
        item: 'basis-[95%] px-2'
      }"
    >
      <template #default="{ item: slideItems }">
        <div class="grid grid-cols-12 grid-rows-2 gap-3">
          <UButton
            v-for="item in slideItems"
            :key="item.category"
            :to="{
              name: cityStore?.routeSlug ? 'city-category' : 'category',
              params: { category: item.category, city: cityStore?.routeSlug }
            }"
            :color="route.params.category === item.category ? 'primary' : 'neutral'"
            variant="soft"
            class="relative block h-16 overflow-hidden rounded-md p-3 pr-0 md:h-20"
            :class="[item.slideColspan]"
          >
            <h3 class="relative z-10 pr-14 text-xs leading-tight font-medium">
              {{ item.label }}
            </h3>
            <NuxtImg
              class="pointer-events-none absolute right-0 bottom-0 h-full w-auto object-cover select-none"
              aria-hidden="true"
              :src="item.image"
              :alt="`Иконка категории ${item.label}`"
              loading="eager"
            />
          </UButton>
        </div>
      </template>
    </UCarousel>

    <!-- Десктопная сетка -->
    <div v-else class="grid grid-cols-12 grid-rows-2 gap-3">
      <UButton
        v-for="item in categories"
        :key="item.category"
        :to="{
          name: cityStore?.routeSlug ? 'city-category' : 'category',
          params: { category: item.category, city: cityStore?.routeSlug }
        }"
        :color="route.params.category === item.category ? 'primary' : 'neutral'"
        variant="soft"
        class="group hover:ring-primary-500/20 dark:hover:ring-primary-400/20 relative block h-20 overflow-hidden rounded-md p-3 pr-0 transition-all hover:ring-2"
        :class="[item.colSpan]"
      >
        <h3 class="relative z-10 pr-16 text-sm leading-tight font-medium">
          {{ item.label }}
        </h3>
        <NuxtImg
          class="pointer-events-none absolute right-0 bottom-0 h-full w-auto object-cover transition-transform select-none group-hover:scale-105"
          :alt="`Иконка категории ${item.label}`"
          :src="item.image"
          loading="eager"
        />
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";

const { $device } = useNuxtApp();
const cityStore = useCityStore();
const route = useRoute();
const { categories } = useDashboard();

const slides = computed(() => {
  const chunkSize = 5;
  const sortedCategories = [...categories].sort((a, b) => a.index - b.index);
  const result: (typeof categories)[] = [];
  for (let i = 0; i < sortedCategories.length; i += chunkSize) {
    result.push(sortedCategories.slice(i, i + chunkSize));
  }
  return result;
});
</script>

<style scoped>
@keyframes peek {
  0% {
    transform: translateX(0);
  }
  30% {
    transform: translateX(-20%);
  }
  60% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-peek :deep([data-carousel-content]) {
  animation: peek 2s ease-in-out;
}
</style>
