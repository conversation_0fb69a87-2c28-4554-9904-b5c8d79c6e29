<template>
  <UNavigationMenu
    class="mb-0"
    :ui="{
      list: 'gap-2',
      link: 'bg-gray-50 dark:bg-zinc-800 dark:hover:bg-zinc-700 hover:bg-gray-100 rounded-lg'
    }"
    :items="items"
  >
    <template #item-leading="{ item }">
      <UIcon v-if="item?.icon" :name="item.icon" class="text-lg text-red-800" />
    </template>
  </UNavigationMenu>
</template>

<script setup lang="ts">
const items = ref([
  [
    {
      label: "Магазины",
      icon: "i-lucide-store",
      to: "#"
    },
    {
      class: "hidden  md:flex",
      label: "Стрелковые стенды",
      icon: "i-lucide-land-plot",
      to: "#"
    },
    {
      class: "hidden  md:flex",
      label: "Тиры",
      icon: "i-lucide-between-horizontal-end",
      to: "#"
    },
    {
      class: "hidden sm:flex",
      label: "Мероприятия",
      icon: "i-lucide-ticket",
      to: "/events"
    }
  ],
  [
    {
      label: "О проекте",
      to: "#"
    },
    {
      label: "Правила",
      to: "#"
    }
  ]
]);
</script>
