<template>
  <div class="flex flex-wrap gap-1.5">
    <UButton color="neutral" :variant="isNoFilters ? 'solid' : 'outline'" @click="resetFilters">
      Все
    </UButton>
    <FiltersRegion />
    <FiltersPrice />
    <template v-for="(filter, filter_name) in filters" :key="filter_name">
      <UButtonGroup v-if="filter_name === 'gun_types'">
        <UButton
          v-if="state[filter_name]"
          icon="i-lucide-x"
          square
          color="neutral"
          variant="soft"
          @click="setGunType()"
        />
        <UButton
          v-for="type in getValues(filter_name, filter.values)"
          :key="type.value"
          color="neutral"
          :variant="state.gun_types === type.value ? 'subtle' : 'outline'"
          @click="setGunType(type.value)"
        >
          {{ type.label }}
        </UButton>
      </UButtonGroup>
      <UButtonGroup v-else-if="filter_name !== 'types'">
        <UButton
          v-if="state[filter_name]"
          icon="i-lucide-x"
          square
          color="neutral"
          variant="soft"
          @click="resetFilter(filter_name)"
        />
        <USelect
          v-model="state[filter_name]"
          :ui="{ content: 'w-full', placeholder: 'text-(--ui-text)' }"
          :items="getValues(filter_name, filter.values)"
          :placeholder="filter.label"
          color="neutral"
          :variant="state[filter_name] ? 'subtle' : 'outline'"
          @update:model-value="setFilter(filter_name)"
        />
      </UButtonGroup>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PostFilters } from "~/types/listing";

const { $metrika } = useNuxtApp();
const router = useRouter();
const route = useRoute();
const props = defineProps<{
  filters: PostFilters;
}>();

const state = reactive<{ [key: string]: string | number | null }>({});
const isNoFilters = computed(() => Object.keys(route.query).length === 0 && !route.params?.type);

watch(
  () => state.gun_types,
  (newGunType, oldGunType) => {
    if (newGunType !== oldGunType) {
      delete state.calibers;
    }
  }
);

const getValues = (filter_name, values) => {
  if (filter_name === "calibers" && state?.gun_types) {
    return values.filter((item) => item.value.startsWith(state.gun_types));
  }

  return values;
};

const resetFilter = async (filter_name) => {
  const newQuery = { ...route.query };
  Reflect.deleteProperty(newQuery, filter_name);
  Reflect.deleteProperty(state, filter_name);
  await router.replace({ query: newQuery });
};
const setGunType = async (type) => {
  state.gun_types = type;
  state.calibers = null;
  delete route.query.calibers;

  await navigateTo({
    name: route.params?.city ? "city-category" : "category",
    params: {
      city: route.params?.city,
      category: route.params.category,
      type
    },
    query: route.query
  });
};
const setFilter = async (filter_name) => {
  $metrika.reachGoal("list_set_filter");

  const params = route.params;
  if (filter_name === "calibers" && state.calibers && !state.gun_types) {
    // Например, можно установить gun_types, извлекая его из calibers
    const [gunType] = state.calibers.split("-"); // Берем первую часть до '-'
    state.gun_types = gunType;
    params.type = gunType;

    await nextTick();
  }
  await router.replace({
    params,
    query: { ...route.query, ...{ [filter_name]: state[filter_name] } }
  });
};
const resetFilters = async () => {
  Object.keys(state).forEach((key) => Reflect.deleteProperty(state, key));
  await navigateTo({
    path: `/${route?.params?.city}/${route?.params?.category}`
  });
};

onMounted(() => {
  // Заполняем state только значениями из route.query, если они есть в filters
  Object.keys(route.query).forEach((key) => {
    if (props.filters[key]) {
      state[key] = route.query[key]; // Если фильтр есть в filters, добавляем его в state
    }
  });
  if (route.params.type) {
    state.gun_types = route.params.type;
  }
});
</script>
