<script setup lang="ts">
const { authOpenModal } = useDashboard();
const { isAuthenticated } = useSanctumAuth();

const items = [
  {
    label: "Закон об оружии",
    to: "/zakon"
  },
  {
    label: "Публичная оферта",
    to: "/oferta"
  },
  {
    label: "Политика конфиденциальности",
    to: "/privacy_policy"
  },
  {
    label: "Пользовательское соглашение",
    to: "/terms"
  },
  {
    label: "О Проекте",
    to: "/about"
  },
  {
    label: "Платные услуги",
    to: "/promo"
  }
];

const sendMessage = async () => {
  if (!isAuthenticated.value) return (authOpenModal.value = true);

  navigateTo("/inbox/support");
};
</script>

<template>
  <UFooter
    :ui="{
      container: 'pt-6 lg:pt-6 border-t border-[var(--ui-border)] mt-12 lg:mt-12',
      bottom: 'py-0 lg:py-0 mb-4',
      left: 'justify-start'
    }"
  >
    <template #left>
      <div class="flex flex-col gap-1 text-sm text-(--ui-text-muted)">
        <p>Ганпост © {{ new Date().getFullYear() }}</p>
        <p class="block">
          Администрация:
          <a class="hover:underline" href="mailto:<EMAIL>"><EMAIL></a>
        </p>
        <p class="block">
          Поддержка:
          <span class="cursor-pointer hover:underline" @click="sendMessage">написать в чат</span>
        </p>
      </div>
    </template>

    <template #right>
      <ul class="flex flex-wrap gap-x-2 gap-y-1 lg:justify-end">
        <li v-for="item in items" :key="item.to" class="text-sm text-(--ui-text-muted)">
          <NuxtLink target="_blank" class="hover:underline" :to="item.to">
            {{ item.label }}
          </NuxtLink>
        </li>
      </ul>
    </template>

    <template #bottom>
      <UContainer class="text-sm text-(--ui-text-highlighted)">
        <p class="w-full text-justify">
          Сайт gunpost.ru предоставляет исключительно информационную площадку и не осуществляет
          продажу или покупку представленных товаров; не функционирует как магазин; не является
          стороной либо гарантом заключаемых сделок; не извлекает материальной выгоды из
          правоотношений возникающих между частными и юридическими лицами.
        </p>
      </UContainer>
    </template>
  </UFooter>
</template>
