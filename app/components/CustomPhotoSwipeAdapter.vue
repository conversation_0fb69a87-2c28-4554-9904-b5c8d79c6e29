<script setup lang="ts">
import type { PhotoRendererMetadata } from "vue-photo-album";
import { computed } from "vue";

const props = defineProps<PhotoRendererMetadata>();

// Проверяем, является ли URL абсолютным
const isAbsoluteUrl = (url: string) => {
  return /^https?:\/\//i.test(url);
};

// Обрабатываем URL для PhotoSwipe
const processedUrl = computed(() => {
  if (!props.photo.src) return "";
  return isAbsoluteUrl(props.photo.src) ? props.photo.src : `/${props.photo.src}`;
});
</script>

<template>
  <a
    :href="processedUrl"
    :data-pswp-width="photo?.width"
    :data-pswp-height="photo?.height"
    :data-pswp-srcset="photo?.srcSet"
    rel="noopener noreferrer"
    target="_blank"
    @click.prevent=""
  >
    <slot />
  </a>
</template>
