<template>
  <USelectMenu
    v-model="selectedColorMode"
    :items="colorModeOptions"
    :search-input="false"
    :icon="selectedColorMode?.icon"
  />
</template>

<script setup lang="ts">
defineProps<{
  modelValue: string | undefined;
}>();
const emit = defineEmits(["update:modelValue"]);
const colorMode = useColorMode();
const colorModeOptions = [
  { label: "Системная", value: "system", icon: "i-ph-desktop" },
  { label: "Светлая", value: "light", icon: "i-ph-sun" },
  { label: "Тёмная", value: "dark", icon: "i-ph-moon" }
];

const selectedColorMode = computed({
  get() {
    return (
      colorModeOptions.find((option) => option.value === colorMode.preference) ||
      colorModeOptions[0]
    );
  },
  set(option) {
    colorMode.preference = option.value;
    emit("update:modelValue", option.value);
  }
});
</script>
