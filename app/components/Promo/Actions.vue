<template>
  <div class="space-y-2">
    <URadioGroup v-model="type" variant="table" label-key="name" value-key="type" :items="types">
      <template #description="{ item }">
        {{ item.description }} <span v-if="item.duration > 1">на {{ item.duration }} дней</span>
      </template>
      <template #label="{ item }">
        {{ item.name }}
        <UBadge size="sm" class="ml-1" variant="outline">
          {{ $currency(item.price) }}
        </UBadge>
      </template>
    </URadioGroup>
    <UButton :loading="isLoading" size="xl" icon="i-lucide-wallet" block @click="tryPromotion"
      >Оплатить {{ $currency(types.find((t) => t.type === type)?.price) }}</UButton
    >
    <UButton to="/promo" target="_blank" icon="i-lucide-info" variant="link" color="neutral" block
      >Подробнее о платных услугах</UButton
    >
    <div id="payment-form"></div>
  </div>
</template>

<script setup lang="ts">
import type { Post } from "~/types/post";

const props = defineProps<{
  post: Post;
  types: object;
  size?: "sm" | "md" | "xs";
}>();
const { $currency } = useNuxtApp();
const toast = useToast();
const client = useSanctumClient();
const emit = defineEmits(["refresh"]);
const paymentFormModal = ref(false);
const type = ref(props.types[0]?.type);
const isLoading = ref(false);

useHead({
  script: [
    {
      src: "https://yookassa.ru/checkout-widget/v1/checkout-widget.js",
      type: "text/javascript",
      async: true
    }
  ]
});

async function makeYooOrderModal() {
  isLoading.value = true;

  try {
    const response = await client(`/payment/${props.post.slug}`, {
      method: "POST",
      body: {
        promotion: type.value
      }
    });

    // Инициализация виджета. Все параметры обязательные.
    paymentFormModal.value = true;

    await nextTick();

    const checkout = new window.YooMoneyCheckoutWidget({
      confirmation_token: response.url.confirmation.confirmation_token, // Токен, который вы получили после создания платежа
      customization: {
        modal: true
      },
      error_callback: function (error) {
        console.error("error_callback", error);
        toast.add({
          color: "error",
          title: "Не удалось выполнить платеж"
        });
        isLoading.value = false;
      }
    });

    if (!checkout) {
      toast.add({
        color: "error",
        title: "Не удалось выполнить платеж"
      });

      isLoading.value = false;
      checkout.destroy();
      emit("refresh");

      return;
    }

    checkout.on("complete", async () => {
      toast.add({
        color: "success",
        title: "Ваш платеж успешно завершен",
        description: "Данные об оплате появятся в течении нескольких минут"
      });

      isLoading.value = false;
      checkout.destroy();
      emit("refresh");
    });

    checkout.on("fail", (data) => {
      console.error("fail", data);
      toast.add({
        color: "error",
        title: "Не удалось выполнить платеж"
      });

      isLoading.value = false;
      checkout.destroy();
      emit("refresh");
    });

    checkout.on("modal_close", () => {
      isLoading.value = false;
      checkout.destroy();
      emit("refresh");
    });

    // Отображение платежной формы в контейнере
    checkout.render("payment-form");
  } catch (error) {
    console.error("Payment error:", error);
    toast.add({
      color: "error",
      title: "Не удалось выполнить платеж"
    });
    isLoading.value = false;
    emit("refresh");
  }
}

const tryPromotion = async () => {
  isLoading.value = true;

  try {
    await client(`/posts/${props.post.slug}/promotions`, {
      method: "POST",
      body: {
        promotion: type.value
      }
    });

    toast.add({
      icon: "i-lucide-check-circle",
      color: "success",
      title: "Активация продвижения занимает некоторое время, пожалуйста, подождите"
    });

    emit("refresh");
  } catch (error) {
    if (error?.status === 402) {
      await makeYooOrderModal();
    } else {
      emit("refresh");
    }
  }

  isLoading.value = false;
};
</script>
