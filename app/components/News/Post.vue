<template>
  <div class="prose dark:prose-invert prose-p:m-0 prose-ul:m-0 prose-h2:my-4 max-w-none">
    <div v-if="news.data?.image" class="mb-4">
      <img
        :src="news.data?.image"
        :alt="news.data?.title"
        class="mt-0 mb-0 aspect-video w-full rounded-md object-cover"
      />
      <span v-if="news?.data?.image_alt" class="mt-1 text-sm text-[var(--ui-text-muted)]">
        Фото: {{ news.data?.image_alt }}
      </span>
    </div>
    <!-- eslint-disable-next-line vue/no-v-html -->
    <div v-html="news.data?.content" />
  </div>
</template>

<script setup lang="ts">
import type { NewsSingleResponse } from "~/types/news";

defineProps<{
  news: NewsSingleResponse;
}>();
</script>
