<template>
  <div class="sticky top-2 rounded-xl bg-blue-50/50 p-3 dark:bg-blue-900/20">
    <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-1">
      <CardItem v-for="(item, n) in items" :key="n" :item="item" />
    </div>

    <UButton size="sm" to="/promo" block variant="soft"> Как попасть сюда? </UButton>
  </div>
</template>

<script setup lang="ts">
defineProps({
  items: {
    type: Array,
    required: true
  }
});
</script>
