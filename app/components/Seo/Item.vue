<template>
  <UCard>
    <template #header>
      Путь:
      <NuxtLink :to="item.path" target="_blank">{{ item.path }}</NuxtLink>
    </template>
    <ul>
      <li><strong>title: </strong> {{ item.title }}</li>
      <li><strong>meta_description: </strong> {{ item.meta_description }}</li>
      <li><strong>meta_keywords: </strong> {{ item.meta_keywords }}</li>
      <li><strong>h1: </strong> {{ item.h1 }}</li>
      <!--      <li><strong>content: </strong> <p class="line-clamp-2">{{ item.content }}</p></li> -->
    </ul>
    <template #footer>
      <ModalsSeoPathItemModal :item="item" />
    </template>
  </UCard>
</template>

<script setup lang="ts">
defineProps<{
  item: {
    type: object;
    required: true;
  };
}>();
</script>
