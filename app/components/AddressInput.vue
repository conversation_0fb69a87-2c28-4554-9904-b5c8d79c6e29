<script setup lang="ts">
import { refDebounced } from "@vueuse/core";
import type { Address, City } from "~/types/post";

const client = useSanctumClient();
const searchTerm = ref("");
const searchTermDebounced = refDebounced(searchTerm, 500);
const searchSelected = ref(null);
const props = withDefaults(
  defineProps<{
    modelValue: Address | undefined;
    id?: string;
    city?: City;
  }>(),
  {
    id: "address-input"
  }
);
const emit = defineEmits(["update:modelValue"]);
const query = computed(() => {
  const cityName = props.city?.label ?? "";
  const searchText = searchTermDebounced.value ?? "";
  return searchText ? `${cityName} ${searchText}`.trim() : "";
});

const updateValue = () => {
  emit("update:modelValue", searchSelected.value);
};

const { data, status, refresh } = useAsyncData(`suggest-address:${props.id}`, () =>
  query.value && searchTermDebounced.value
    ? client(`suggest/address`, {
        params: { query: query.value }
      })
    : Promise.resolve({ data: [] })
);

watch(query, () => {
  if (query.value && searchTermDebounced.value) {
    refresh();
  }
});
</script>

<template>
  <UInputMenu
    :id="id"
    v-model:search-term="searchTerm"
    v-model="searchSelected"
    :items="data"
    :loading="status === 'pending'"
    size="xl"
    class="w-full"
    ignore-filter
    icon="i-lucide-map-pinned"
    placeholder="Начните вводить адрес"
    @change="updateValue"
  >
    <template #empty>
      <div v-if="status === 'pending'" class="flex items-center justify-center gap-1.5">
        <span>Идет поиск...</span>
      </div>
      <div
        v-else-if="status === 'success' && !data.length"
        class="flex items-center justify-center gap-1.5"
      >
        <UIcon name="i-lucide-search-x" class="size-4" />
        <span>Ничего не найдено, попробуйте другой запрос</span>
      </div>
    </template>
  </UInputMenu>
</template>
