// plugins/device.client.ts
import { defineNuxtPlugin, useNuxtApp } from "#app";

export default defineNuxtPlugin((nuxtApp) => {
  // 1. Клиент: забираем из payload, который Nuxt автоматически вбросил в window.__NUXT__.payload
  const { device } = useNuxtApp().payload as {
    device: { isMobile: boolean; isTablet: boolean; isDesktop: boolean };
  };

  // 2. Просто provide() без детекции
  nuxtApp.provide("device", device);
});
