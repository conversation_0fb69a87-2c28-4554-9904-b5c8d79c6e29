// plugins/device.server.ts
import MobileDetect from "mobile-detect";
import { defineNuxtPlugin, useRequestHeaders } from "#app";

export default defineNuxtPlugin((nuxtApp) => {
  // 1. Сервер: читаем UA из заголовков
  const headers = useRequestHeaders(["user-agent"]);
  const ua = headers["user-agent"] || "";

  const md = new MobileDetect(ua);
  const isMobile = !!md.phone();
  const isTablet = !!md.tablet();
  const isDesktop = !isMobile && !isTablet;

  // 2. Записываем в SSR‐payload
  nuxtApp.ssrContext!.payload = {
    ...nuxtApp.ssrContext!.payload,
    device: { isMobile, isTablet, isDesktop }
  };

  // 3. И сразу обеспечиваем provide() для серверного рендера
  nuxtApp.provide("device", { isMobile, isTablet, isDesktop });
});
