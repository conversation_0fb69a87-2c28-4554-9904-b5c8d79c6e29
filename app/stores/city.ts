import type { User } from "~/types/auth";
import type { City } from "~/types/post";

export const useCityStore = defineStore("CityStore", {
  state: () => ({
    cities: undefined as City[] | undefined,
    citySlug: undefined as string | undefined,
    needGeolocation: false
  }),
  getters: {
    routeSlug(state) {
      const route = useRoute();
      return route?.params?.city ?? state.citySlug;
    },
    city(state) {
      return state.cities?.find((item: City) => item.value === state.citySlug);
    }
  },
  actions: {
    async fetch() {
      const cookieCity = useCookie("city");

      const user = useSanctumUser<User>();
      const { data } = await useSanctumFetch<{
        cities: City[];
        city: { city_slug: string; city_name: string };
      }>("/cities");

      this.cities = data.value?.cities ?? [];

      if (!cookieCity.value && user.value?.city?.value) {
        // Берем у пользователя
        cookieCity.value = user.value.city.value;
      } else if (!cookieCity.value && data.value?.city?.city_slug) {
        // Берем по ip
        cookieCity.value = data.value.city.city_slug;
      }

      if (!cookieCity.value) {
        // Берем по умолчанию
        cookieCity.value = "moskva";
      }

      this.citySlug = cookieCity.value;
    },
    async setCity(slug: string) {
      const { isAuthenticated, refreshIdentity } = useSanctumAuth();

      const city = useCookie("city");
      city.value = slug;
      this.citySlug = slug;

      if (!isAuthenticated.value) {
        return;
      }

      try {
        await useSanctumFetch("/user/set-city", {
          method: "POST",
          body: { city: slug }
        });
        await refreshIdentity();
      } catch (e) {
        console.error(e);
      }
    }
  }
});
