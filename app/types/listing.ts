import type { LocationQuery } from "vue-router";
import type { Post as PostType, City as PostCity } from "./post";

export interface Post {
  id: number;
  title: string;
  // Добавьте другие поля по необходимости
}

export interface PostMeta {
  current_page: number;
  total: number;
  last_page: number;
  per_page: number;
  // Добавьте другие поля по необходимости
}

export interface PostFilter {
  value: string;
  label: string;
}

export interface PostFilters {
  [key: string]: {
    label: string;
    placeholder: string;
    values: { label: string; value: string }[];
    is_filter?: boolean;
  };
}

export interface Category {
  name: string;
  slug: string;
}

export interface Seo {
  title?: string;
  h1?: string;
  meta_description?: string;
  content?: string;
}

export interface PostsResponse {
  data: PostType[];
  meta: PostMeta;
  filters?: PostFilters;
  category?: Category;
  city?: PostCity;
  vipAds?: PostType[];
  other?: PostType[];
  seo?: Seo;
  is_empty?: boolean;
}

export interface ListingRouteParams {
  category?: string;
  city?: string;
  type?: string;
  query?: LocationQuery;
}

export interface NuxtErrorWithType extends Error {
  statusCode?: number;
  fatal?: boolean;
  type?: string;
}

export interface RouteLocation {
  params: {
    type?: string;
  };
  query: LocationQuery;
}

export interface City {
  label: string;
  name: string;
  name_pred: string;
  value: string;
  region: string;
  posts: number;
  lat: number;
  lng: number;
}
