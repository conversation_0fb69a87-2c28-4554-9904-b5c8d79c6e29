export interface NewsItem {
  loading?: boolean;
  id: number;
  title: string;
  slug: string;
  content: string;
  description: string;
  image?: string;
  cover?: string;
  cover_small?: string;
  image_alt?: string;
  published_at: string;
  status: string;
  created_at: string;
  updated_at: string;
  meta_title: string;
  meta_description: string;
  meta_keywords: string;
}

export interface NewsResponse {
  data: NewsItem[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface NewsSingleResponse {
  data: NewsItem;
  other: NewsItem[];
}
