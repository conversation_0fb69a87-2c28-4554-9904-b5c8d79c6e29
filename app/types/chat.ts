import type { Post } from "./post";
import type { UserAvatar } from "~/types/auth";

// Тип пользователя
export interface ChatUser {
  id: string;
  name: string;
  is_verified?: boolean;
  support_chat_id?: number;
  avatar?: UserAvatar;
}

// Тип изображения в сообщении
export interface ChatImage {
  file?: File;
  src: string;
  width: number;
  height: number;
  srcSet: Array<{
    src: string;
    width: number;
    height: number;
  }>;
}

// Тип сообщения
export interface Message {
  id: number;
  role: "user" | "assistant" | "system" | "data";
  content: string;
  created_at: string;
  user: ChatUser;
  media?: ChatImage[];
}

// Тип чата
export interface Chat {
  id: string;
  post?: Post;
  metadata?: {
    title?: string;
  };
  messages: Message[];
}

export interface ChatUIMessage {
  id: string;
  role: "user" | "assistant" | "system" | "data";
  content: string;
  user?: {
    avatar?: UserAvatar;
  };
}
