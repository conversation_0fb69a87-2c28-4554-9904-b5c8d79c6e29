export interface PostAttribute {
  type: string;
  label: string;
  name: string;
  slug: string;
  required?: boolean;
  placeholder?: string;
  values?: Array<{ value: string; label: string }>;
}

export interface GunType {
  name: string;
}

export interface Caliber {
  name: string;
}

export interface City {
  label: string;
  name: string;
  name_pred: string;
  value: string;
  region: string;
  posts: number;
  lat: number;
  lng: number;
}

export interface Category {
  name: string;
  slug: string;
  registration: boolean;
  value?: string;
  label?: string;
}

export interface Address {
  street: string;
  house: string;
  apartment?: string;
}

export interface PostImage {
  file?: File; // только пока не отправлен
  id?: string; // id медиа-записи, пришедший от сервера
  uploaded?: boolean; // ← новый флаг
  loading?: boolean; // флаг загрузки изображения
  src?: string; // URL изображения для предпросмотра
  width?: number; // ширина изображения
  height?: number; // высота изображения
}

export interface PostImageWithSrcSet {
  src: string;
  width: number;
  height: number;
  srcSet: Array<{
    src: string;
    width: number;
    height: number;
  }>;
}

export interface GeoCoordinates {
  lat: number;
  lon: number;
}

export enum ModerationStatus {
  PENDING = 0,
  APPROVED = 1,
  REJECTED = 2,
  ARCHIVED = 3
}

export interface PostState {
  step: number;
  title: string;
  description: string;
  year: number | null;
  price: number;
  is_trade: boolean;
  is_rebate: boolean;
  can_ship: boolean;
  category?: Category | undefined;
  contact: string;
  registration_type: string;
  address: string | null;
  slug: string | null;
  city?: City;
  images: PostImage[];
  attributes: Record<string, string>;
  data_address: Address | undefined;
  data_registration_address: Address | undefined;
  registration_address: string | null;
  uuid: string | undefined;
  [key: string]: unknown;
}

export interface Post {
  slug: string;
  title: string;
  description: string;
  price: number;
  archived_reason?: string;
  thumbnails?: string[];
  seller_name?: string;
  seller_id?: number;
  seller_avatar?: string;
  address?: string;
  registration_type?: "OLRR" | "SHOP";
  is_trade?: boolean;
  year?: string | number;
  attributes?: PostAttribute[];
  gun_types_name?: string;
  gun_types?: GunType;
  calibers?: Caliber;
  description_short?: string;
  address_geo?: GeoCoordinates;
  registration_geo?: GeoCoordinates;
  category_name?: string;
  city_name?: string;
  city_name_pred?: string;
  category?: string;
  city_slug?: string;
  seller_is_shop?: boolean;
  city?: City;
  source?: string;
  can_ship?: boolean;
  published_at?: string;
  moderation_id?: ModerationStatus;
  images?: PostImageWithSrcSet[];
  favorites?: number;
}

export interface PostResponse {
  post: Post;
  is_preview?: boolean;
  is_favorite?: boolean;
  is_owner?: boolean;
  vipAds?: Post[];
  other?: Post[];
}
