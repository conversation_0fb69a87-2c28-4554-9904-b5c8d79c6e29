export interface UserAvatar {
  alt: string;
  src: string;
}
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role?: "admin" | "user";
  balance: number;
  avatar?: UserAvatar;
  theme?: "light" | "dark" | "system";
  bio?: string;
  has_password?: boolean;
  favorites?: number;
  messages?: number;
  notifications?: number;
  support_chat_id?: string;
  city?: { value: string; label: string };
  telegram_notification?: boolean;
}
