<script setup lang="ts">
import type { NuxtError } from '#app'

const props = defineProps({
  error: Object as () => NuxtError
})

const { isAuthenticated } = useSanctumAuth();

// useSeoMeta({
//   title: props.error?.statusCode === 404 ? "Страница не найдена" : "Что-то пошло не так"
// });

// Устанавливаем статус через nitro
if (import.meta.server) {
  console.log(2)
  // const event = useRequestEvent();
  // console.log(1)
  // if (event) {
  //   setResponseStatus(event, props.error?.statusCode ?? 500)
  // }
}
console.log(3)
</script>

<template>
  <div>
    <AppHeader />

    <UContainer class="flex flex-col items-center justify-center gap-4 pt-4">
      <img class="w-28" alt="gunpost.ru" src="/images/error.png" />
      <h1 class="text-xl font-bold">
        <UBadge size="xl" variant="soft">
          {{ error.statusCode }}
        </UBadge>
        1 Ой, что-то пошло не так
      </h1>
      <p v-if="error.statusCode === 404" class="text-center text-[var(--ui-text-muted)]">
        Похоже, мы не можем найти нужную вам страницу.
      </p>
      <p v-else class="text-center text-[var(--ui-text-muted)]">
        Мы уже знаем о проблеме и работаем над ее решением. Попробуйте позже
      </p>

      <div class="my-4">
        <FiltersCategories />
      </div>

      <UButton size="xl" to="/" icon="i-lucide-undo-2"> Вернуться на главную </UButton>
    </UContainer>
    <ClientOnly>
      <ModalsAboutModal />
      <ModalsAuthModal v-if="!isAuthenticated" />
    </ClientOnly>

    <div class="mt-auto mb-0">
      <AppFooter />
    </div>
  </div>
</template>
