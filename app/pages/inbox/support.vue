<script setup lang="ts">
import type { NuxtErrorWithType } from "~/types/listing";

const client = useSanctumClient();

const { data } = await useAsyncData<{ id: string }>("chats.support", () =>
  client("/chats/support")
);
if (data.value?.id) {
  await navigateTo(`/inbox/${data.value.id}`);
} else {
  throw createError({
    statusCode: 404,
    fatal: true,
    type: "support"
  } as NuxtErrorWithType);
}
</script>
