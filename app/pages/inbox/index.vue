<template>
  <div class="p-4 sm:p-6 lg:py-6">
    <UPageCard title="Ваши сообщения" variant="naked" class="mb-4" />

    <UCard v-if="!chats?.data?.length" class="max-w-sm">
      <UIcon name="i-lucide-laptop-minimal-check" class="size-12 text-(--ui-text-muted)" />
      <p class="mt-2 text-pretty text-(--ui-text-muted)">
        В этом разделе пока пусто. У вас нет сообщений
      </p>
    </UCard>

    <UPageList class="space-y-2">
      <UPageCard
        v-for="(item, index) in chats?.data"
        :key="index"
        :ui="{ container: 'p-2 sm:p-4' }"
        variant="soft"
        orientation="horizontal"
        :class="[item.read_at ? 'cursor-default' : 'cursor-pointer']"
        :to="{ name: 'inbox-id', params: { id: item.id } }"
      >
        <template #leading>
          <CardItemWidget v-if="item?.source" :item="item.source" />
          <div v-else-if="item.id === user.support_chat_id" class="flex gap-2">
            <UIcon name="i-lucide-life-buoy" class="text-success-500 size-10" />
            <div>
              <div class="font-medium">Поддержка ГанПост</div>
              <div class="text-md mb-auto text-[var(--ui-text-muted)] md:text-sm">
                Официальный канал связи с администрацией
              </div>
            </div>
          </div>
          <div
            v-else-if="item?.metadata?.title"
            class="pl-2 text-lg font-medium md:line-clamp-1 md:text-sm"
          >
            {{ item.metadata.title }}
          </div>
        </template>
        <div class="col-span-2 -mt-4">
          <div class="mb-2 flex items-center gap-2">
            <UBadge
              v-if="item.unread_messages"
              size="sm"
              icon="i-lucide-message-square-dot"
              color="neutral"
              >{{ item.unread_messages }}</UBadge
            >
            <span>{{ item.subject }}</span>
          </div>
          <div
            v-if="item?.latest_message"
            class="space-y-1 border-l border-[var(--ui-border)] pl-2"
          >
            <p class="text-[var(--ui-text-muted)] md:line-clamp-3">
              {{ item?.latest_message.content }}
            </p>
            <div class="flex items-center gap-2">
              <span class="flex items-center gap-1 font-semibold">
                <UUser
                  :name="item?.latest_message?.user?.name"
                  icon="i-lucide-badge-check"
                  :avatar="item?.latest_message?.user?.avatar"
                >
                  <template #name>
                    <UIcon
                      v-if="item?.latest_message?.user?.is_verified"
                      class="text-[var(--ui-primary)]"
                      name="i-lucide-badge-check"
                    />
                    {{ item?.latest_message?.user?.name }}
                  </template>
                  <template #description>
                    <NuxtTime date-style="long" time-style="short" :datetime="item.created_at" />
                  </template>
                </UUser>
              </span>
            </div>
          </div>
        </div>
      </UPageCard>
    </UPageList>

    <Pagination
      :meta="chats.meta"
      :page="page"
      :disabled="status === 'pending'"
      @set-page="page = $event"
    />
  </div>
</template>

<script setup lang="ts">
import type { User } from "~/types/auth";

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const user = useSanctumUser<User>();
const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page || 1);

const { data: chats, status } = await useAsyncData(
  "chats",
  () =>
    client("/chats", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);
</script>
