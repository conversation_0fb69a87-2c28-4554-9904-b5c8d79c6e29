<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="SEO Страницы">
        <template #right>
          <Search />
          <AuthUser />
          <ModalsSeoPathItemModal
            :item="{
              path: '',
              title: '',
              meta_description: '',
              meta_keywords: '',
              h1: '',
              content: ''
            }"
            @refresh="refresh"
          />
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar>
        <template #right>
          <UInput v-model="searchTerm" placeholder="Поиск по path" icon="i-lucide-search" />
        </template>
      </UDashboardToolbar>
    </template>

    <template #body>
      <UCard v-if="!seo?.data?.length" class="max-w-sm">
        <UIcon name="i-lucide-flag-triangle-right" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)"></p>
      </UCard>

      <div class="flex w-full flex-col gap-4 sm:gap-6 lg:max-w-2xl lg:gap-12">
        <div v-if="seo.data">
          <SeoItem v-for="item in seo.data" :key="item.id" :item="item" @refresh="refresh" />
        </div>

        <Pagination :meta="seo.meta" :page="page" :disabled="status === 'pending'" />
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
const client = useSanctumClient();
definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const page = ref(1);
const searchTerm = ref("");

const {
  data: seo,
  status,
  refresh
} = await useAsyncData(
  `seo:moderation:${page.value}`,
  () =>
    client("/moderation/seo", {
      params: {
        page: page.value,
        q: searchTerm.value
      }
    }),
  {
    watch: [page, searchTerm]
  }
);
</script>
