<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="Модерация">
        <template #right>
          <Search />
          <AuthUser />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="lg:max-w-2xl">
        <UTabs v-model="activeTab" :items="tabs" :content="false" class="w-full" />
      </div>

      <UCard v-if="!posts?.data?.length" class="max-w-sm">
        <UIcon name="i-lucide-ethernet-port" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)">.</p>
      </UCard>
      <div class="flex w-full flex-col gap-4 sm:gap-6 lg:max-w-2xl lg:gap-12">
        <div class="space-y-4">
          <div v-for="post in posts.data" :key="post.slug">
            <AdminRowCardSimple :post="post" @refresh="refresh" />
          </div>

          <UPagination
            v-if="posts?.meta?.last_page > 1"
            v-model:page="page"
            class="mt-4"
            :show-controls="false"
            :items-per-page="posts.meta.per_page"
            :total="posts.meta.total"
          />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
import type { TabsItem } from "@nuxt/ui";

const client = useSanctumClient();
definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const page = ref(1);
const activeTab = ref("0");
const tabs = ref<TabsItem[]>([
  {
    label: "Ожидают модерации"
  },
  {
    label: "Одобренные"
  },
  {
    label: "Заблокированные"
  }
]);

const { data: posts, refresh } = await useAsyncData(
  `posts:moderation:${page.value}`,
  () =>
    client("/moderation/posts", {
      params: {
        page: page.value,
        moderation: activeTab.value
      }
    }),
  {
    watch: [page, activeTab]
  }
);
</script>
