<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="события">
        <template #right>
          <Search />
          <AuthUser />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UPageCard title="Редактирование события" variant="naked">
        <template #footer>
          <UButton
            size="md"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="outline"
            to="/admin/events"
          >
            Назад
          </UButton>
        </template>
      </UPageCard>

      <div class="max-w-3xl space-y-4">
        <AdminSeoCheckDetails v-if="data?.data?.check?.status === 'done'" :item="data.data" />

        <UForm ref="form" :state="state" class="space-y-4" @submit="onSubmit">
          <UCard>
            <template #header> SEO Полня </template>
            <div class="space-y-4">
              <UFormField size="xl" required name="meta_title" label="Meta Title">
                <UInput
                  v-model="state.meta_title"
                  :maxlength="70"
                  aria-describedby="character-count"
                  :ui="{ trailing: 'pointer-events-none' }"
                  class="w-full"
                >
                  <template #trailing>
                    <div
                      id="character-count"
                      class="text-xs text-(--ui-text-muted) tabular-nums"
                      aria-live="polite"
                      role="status"
                    >
                      {{ state.meta_title?.length ?? 0 }}/70
                    </div>
                  </template>
                </UInput>
              </UFormField>
              <UFormField size="xl" required name="meta_description" label="Meta Description">
                <UInput
                  v-model="state.meta_description"
                  :maxlength="160"
                  aria-describedby="character-count"
                  :ui="{ trailing: 'pointer-events-none' }"
                  class="w-full"
                >
                  <template #trailing>
                    <div
                      id="character-count"
                      class="text-xs text-(--ui-text-muted) tabular-nums"
                      aria-live="polite"
                      role="status"
                    >
                      {{ state.meta_description?.length ?? 0 }}/160
                    </div>
                  </template>
                </UInput>
              </UFormField>
              <UFormField name="meta_keywords" label="Meta Keywords">
                <UInput v-model="state.meta_keywords" maxlength="255" size="xl" class="w-full" />
              </UFormField>
            </div>
          </UCard>
          <UFormField size="xl" required name="title" label="Заголовок">
            <UInput
              v-model="state.title"
              :maxlength="60"
              aria-describedby="character-count"
              :ui="{ trailing: 'pointer-events-none' }"
              class="w-full"
            >
              <template #trailing>
                <div
                  id="character-count"
                  class="text-xs text-(--ui-text-muted) tabular-nums"
                  aria-live="polite"
                  role="status"
                >
                  {{ state.title?.length ?? 0 }}/60
                </div>
              </template>
            </UInput>
          </UFormField>

          <UFormField required size="xl" name="city" label="Город проведения по базе gunpost">
            <USelectMenu
              v-model="state.city"
              :search-input="{ placeholder: 'Укажите город...' }"
              size="xl"
              class="w-full"
              :items="cityStore.cities"
            />
          </UFormField>

          <UFormField name="place" label="Место проведения">
            <UInput
              v-model="state.place"
              placeholder="Например: Олимпийский"
              maxlength="255"
              size="xl"
              class="w-full"
            />
          </UFormField>

          <UFormField name="address" label="Адрес проведения">
            <UInput
              v-model="state.address"
              placeholder="Например: Ленина 1"
              maxlength="255"
              size="xl"
              class="w-full"
            />
          </UFormField>

          <div class="flex gap-4">
            <UFormField name="start_date" label="Дата начала">
              <UInput v-model="state.start_date" type="date" size="xl" class="w-full" />
            </UFormField>
            <UFormField name="end_date" label="Дата окончания">
              <UInput v-model="state.end_date" type="date" size="xl" class="w-full" />
            </UFormField>
          </div>

          <UFormField
            name="image_new"
            label="Картинка"
            description="JPG, GIF или PNG. До 1MB."
            class="flex justify-between gap-4 max-sm:flex-col sm:items-center"
          >
            <div class="flex flex-wrap items-center gap-3">
              <UAvatar :src="state.image_preview" :alt="state.title" size="lg" />
              <UButton label="Выбрать" color="neutral" @click="onFileClick" />
              <input
                ref="fileRef"
                type="file"
                class="hidden"
                accept=".jpg, .jpeg, .png, .gif"
                @change="onFileChange"
              />
            </div>
          </UFormField>

          <UFormField name="image_alt" label="Описание источника картинки">
            <UInput
              v-model="state.image_alt"
              size="xl"
              class="w-full"
              placeholder="Например: Комитет по физкультуре и спорту Ленобласти"
            />
          </UFormField>

          <div>
            <img
              v-if="data?.data?.image"
              :src="data?.data?.image"
              alt="Картинка"
              class="h-48 w-1/2 rounded-md object-cover"
            />
            <p v-if="data?.data?.image_alt" class="mt-1 text-sm text-[var(--ui-text-muted)]">
              {{ data?.data?.image_alt }}
            </p>
          </div>

          <UFormField required size="xl" name="status" label="Статус">
            <USelect
              v-model="state.status"
              size="xl"
              class="w-full"
              :items="['published', 'draft']"
            />
          </UFormField>

          <UFormField required size="xl" name="description" label="Описание">
            <UTextarea v-model="state.description" size="xl" class="w-full" />
          </UFormField>

          <UFormField required size="xl" name="content" label="Контент">
            <Editor v-model="state.content" />
          </UFormField>

          <div v-if="data?.data?.title">
            <div class="w-full space-y-1">
              <UInput
                v-for="image in state.images"
                :key="image.src"
                class="w-full"
                :value="image.src"
                :avatar="{
                  src: image.src
                }"
              >
                <template v-if="image.src?.length" #trailing>
                  <UTooltip text="Copy to clipboard" :content="{ side: 'right' }">
                    <UButton
                      color="neutral"
                      variant="link"
                      size="sm"
                      icon="i-lucide-copy"
                      aria-label="Copy to clipboard"
                      @click="copy(image.src)"
                    />
                  </UTooltip>
                </template>
              </UInput>
            </div>

            <AdmineventsPhoto :images="state.images" :slug="route.params.slug" @update="refresh" />
          </div>
          <UAlert
            v-else
            variant="soft"
            color="warning"
            title="Для добавления фото нужно сохранить Событие"
          />

          <UButton type="submit" size="xl" :loading="isLoading" block>Сохранить</UButton>
        </UForm>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
import type { NuxtErrorWithType } from "~/types/listing";

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

defineShortcuts({
  meta_s: () => {
    onSubmit();
  }
});

const client = useSanctumClient();
const cityStore = useCityStore();
const route = useRoute();
const toast = useToast();
const form = useTemplateRef("form");
const isLoading = ref(false);
const formData = new FormData();
const fileRef = useTemplateRef("fileRef");

const { data, refresh } = await useAsyncData(`events:moderation:${route.params.slug}`, () =>
  client(`/moderation/events/${route.params.slug}`)
);

if (route.params.slug !== "create" && !data.value?.data) {
  throw createError({
    statusCode: 404,
    fatal: true,
    type: "events"
  } as NuxtErrorWithType);
}

const state = reactive({
  title: data.value?.data?.title || "",
  description: data.value?.data?.description || "",
  content: data.value?.data?.content || "",
  status: data.value?.data?.status || "draft",
  start_date: data.value?.data?.start_date || "",
  end_date: data.value?.data?.end_date || "",
  meta_title: data.value?.data?.meta_title || "",
  meta_description: data.value?.data?.meta_description || "",
  meta_keywords: data.value?.data?.meta_keywords || "",
  place: data.value?.data?.place || "",
  address: data.value?.data?.address || "",
  address_geo: data.value?.data?.address_geo || "",
  image: data.value?.data?.image || "",
  image_preview: "",
  image_alt: data.value?.data?.image_alt || "",
  image_new: null,
  images: data.value?.data?.media || [],
  city: data.value?.data?.city || cityStore?.city
});

watch(
  () => data.value?.data,
  () => {
    state.images = data.value?.data.media || [];
  }
);

const onSubmit = async () => {
  isLoading.value = true;
  try {
    for (const [key, value] of Object.entries(state)) {
      if (key === "city") {
        console.log("value", value.value);
        formData.append("city_id", value.value);
      } else if (value) {
        formData.append(key, value);
      }
    }

    const res = await client(
      `/moderation/events/${route.params.slug === "create" ? "" : route.params.slug}`,
      {
        method: "POST",
        body: formData
      }
    );

    if (res?.slug) {
      toast.add({
        title: "Событие успешно сохранена",
        description: "Событие успешно сохранена",
        icon: "i-lucide-check-circle",
        color: "success"
      });
      if (res?.slug !== route.params.slug) {
        await navigateTo(`/admin/events/${res.slug}`);
        location.reload();
      }
    } else {
      toast.add({
        title: "Событие не сохранена",
        description: "Событие не сохранена",
        icon: "i-lucide-check-circle",
        color: "success"
      });
    }
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      form.value?.setErrors(err.bag);
    }
  }

  await refresh();
  isLoading.value = false;
};

function onFileChange(e: Event) {
  const input = e.target as HTMLInputElement;

  if (!input.files?.length) {
    return;
  }

  formData.append("image_new", input.files[0]);
  state.image_preview = URL.createObjectURL(input.files[0]!);
}

function onFileClick() {
  fileRef.value?.click();
}

function copy(value: string) {
  navigator.clipboard.writeText(value);
  toast.add({
    title: "Ссылка скопирована",
    icon: "i-lucide-check-circle",
    color: "success"
  });
}
</script>
