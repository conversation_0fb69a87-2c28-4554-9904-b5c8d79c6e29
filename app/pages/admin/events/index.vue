<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="Новости">
        <template #right>
          <Search />
          <AuthUser />
          <UButton class="shrink-0" to="/admin/events/create" color="primary" icon="i-lucide-plus">
            Добавить событие
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UCard v-if="!events?.data?.length" class="max-w-sm">
        <UIcon name="i-lucide-eventspaper" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)">События пока не добавлены</p>
      </UCard>

      <div class="flex w-full flex-col gap-4 sm:gap-6 lg:max-w-2xl lg:gap-12">
        <div v-if="events.data" class="space-y-4">
          <UCard v-for="item in events.data" :key="item.slug" class="">
            <template #header>
              <div>
                {{ item.title }}
              </div>
              <NuxtTime
                v-if="item.start_date"
                class="text-sm text-(--ui-text-toned)"
                :datetime="item.start_date"
                time-style="short"
                date-style="short"
              />
              -
              <NuxtTime
                v-if="item.end_date"
                class="text-sm text-(--ui-text-toned)"
                :datetime="item.end_date"
                time-style="short"
                date-style="short"
              />
            </template>
            <NuxtLink :to="`/admin/events/${item.slug}`" class="flex items-end gap-4">
              <img
                v-if="item.image"
                :src="item.image"
                :alt="item.title"
                class="h-18 w-18 object-cover"
              />
              <div class="space-y-2">
                <div class="text-base text-pretty text-(--ui-text-muted)">
                  {{ item.description }}
                </div>
              </div>
            </NuxtLink>
          </UCard>
        </div>

        <Pagination
          :meta="events.meta"
          :disabled="status === 'pending'"
          @set-page="page = $event"
        />
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page ? Number(route.query.page) : 1);

const { data: events, status } = await useAsyncData(
  `events:events:${page.value}`,
  () =>
    client("/moderation/events", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);
</script>
