<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="Новости">
        <template #right>
          <Search />
          <AuthUser />
          <UButton class="shrink-0" to="/admin/news/create" color="primary" icon="i-lucide-plus">
            Добавить новость
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UCard v-if="!news?.data?.length" class="max-w-sm">
        <UIcon name="i-lucide-newspaper" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)">Новости пока не добавлены</p>
      </UCard>

      <div class="flex w-full flex-col gap-4 sm:gap-6 lg:max-w-2xl lg:gap-12">
        <div v-if="news.data" class="space-y-4">
          <UCard v-for="item in news.data" :key="item.slug" class="">
            <template #header>
              <div>
                {{ item.title }}
              </div>
              <NuxtTime
                v-if="item.published_at"
                class="text-sm text-(--ui-text-toned)"
                :datetime="item.published_at"
                time-style="short"
                date-style="short"
              />
            </template>
            <NuxtLink :to="`/admin/news/${item.slug}`" class="flex items-end gap-4">
              <img
                v-if="item.image"
                :src="item.image"
                :alt="item.title"
                class="h-18 w-18 object-cover"
              />
              <div class="space-y-2">
                <div class="text-base text-pretty text-(--ui-text-muted)">
                  {{ item.description }}
                </div>
              </div>
            </NuxtLink>
            <template #footer>
              <UButton
                v-if="!item.check"
                color="primary"
                icon="i-lucide-check"
                variant="outline"
                size="xs"
                @click="sendToSeoCheck(item)"
              >
                Проверить SEO
              </UButton>
              <UBadge v-else-if="item.check?.status !== 'done'" variant="soft" color="primary">
                Статус: {{ item.check?.status }}
              </UBadge>
              <AdminSeoCheck v-if="item.check?.status === 'done'" :item="item" />
            </template>
          </UCard>
        </div>

        <Pagination :meta="news.meta" :disabled="status === 'pending'" @set-page="page = $event" />
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
import type { NewsItem, NewsResponse } from "~/types/news";

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const client = useSanctumClient();
const toast = useToast();
const route = useRoute();
const page = ref(route.query.page ? Number(route.query.page) : 1);

const {
  data: news,
  status,
  refresh
} = await useAsyncData<NewsResponse>(
  `news:moderation:${page.value}`,
  () =>
    client("/moderation/news", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);

const sendToSeoCheck = async (item: NewsItem) => {
  // item.loading = true;

  try {
    await client(`/moderation/news/${item.slug}/check`, {
      method: "POST"
    });
  } catch (error) {
    toast.add({
      title: "Ошибка при отправке на проверку",
      description: error.message ?? "Попробуйте позже"
    });
  }

  // item.loading = false;
  refresh();
};
</script>
