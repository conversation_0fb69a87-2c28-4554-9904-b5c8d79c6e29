<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="Жалобы">
        <template #right>
          <Search />
          <AuthUser />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UCard v-if="!reports?.data?.length" class="max-w-sm">
        <UIcon name="i-lucide-flag-triangle-right" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)"></p>
      </UCard>

      <div class="flex w-full flex-col gap-4 sm:gap-6 lg:max-w-2xl lg:gap-12">
        <div v-if="reports.data" class="space-y-4">
          <UCard v-for="report in reports.data" :key="report.id" :ui="{ body: 'p-0 sm:p-0' }">
            <template #header>
              <UUser
                v-bind="report?.user"
                :name="report.user?.name"
                :description="report.user?.phone ?? report?.email"
              />
            </template>
            <AdminRowCardSimple v-if="report?.post" :post="report.post" @refresh="refresh" />
            <div v-else class="p-3">.</div>
            <template #footer>
              <div class="prose dark:prose-invert">
                {{ report.message }}
              </div>
            </template>
          </UCard>
        </div>

        <Pagination :meta="reports.meta" :page="page" :disabled="status === 'pending'" />
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
const client = useSanctumClient();
definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const page = ref(1);

const {
  data: reports,
  status,
  refresh
} = await useAsyncData(
  `reports:moderation:${page.value}`,
  () =>
    client("/moderation/reports", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);
</script>
