<template>
  <div>
    <UPageCard
      title="Ваше избранное"
      description="Сохраняйте понравившиеся предложения в избранное и просматривайте их с любого устройства."
      variant="naked"
      class="mb-4"
    />

    <UCard v-if="!favorites?.data?.length" class="max-w-sm">
      <UIcon name="i-lucide-laptop-minimal-check" class="size-12 text-(--ui-text-muted)" />
      <p class="mt-2 text-pretty text-(--ui-text-muted)">Ваш список избранного пока пуст.</p>
    </UCard>

    <div class="mt-2 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
      <CardItem v-for="(item, n) in favorites.data" :key="n" :item="item" />
    </div>

    <UPagination
      v-if="favorites?.meta?.last_page > 1"
      v-model:page="page"
      class="mt-4"
      :show-controls="false"
      :items-per-page="favorites.meta.per_page"
      :total="favorites.meta.total"
    />
  </div>
</template>

<script setup lang="ts">
const client = useSanctumClient();

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const page = ref(1);

const { data: favorites } = await useAsyncData(
  `favorites:${page.value}`,
  () =>
    client("/user/favorites", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);
</script>
