<template>
  <div>
    <UPageCard
      title="Ваш кошелек"
      description="В кошельке отображается Ваш текущий баланс, который Вы можете потратить применяя платные услуги к Вашим объявлениям."
      variant="naked"
      class="mb-4"
    />

    <div v-if="false" class="mb-6">
      <h3 class="text-lg">Баланс</h3>
      <span class="text-2xl font-bold">{{ user.balance }}</span>
    </div>

    <div v-if="false" class="mb-6">
      <UButtonGroup v-model="amount" v-maska="options" size="xl">
        <UInput v-model="amount" placeholder="0 ₽" :min="100" />

        <UButton :disabled="amountInt <= 10" @click="makeDeposit"> Пополнить </UButton>
      </UButtonGroup>
      <div class="mt-4 flex gap-2">
        <UButton size="sm" color="neutral" @click="makeDepositBySum(300)"> 300₽ </UButton>
        <UButton size="sm" color="neutral" @click="makeDepositBySum(600)"> 600₽ </UButton>
        <UButton size="sm" color="neutral" @click="makeDepositBySum(900)"> 900₽ </UButton>
      </div>
    </div>

    <UPageCard title="История транзакций" variant="naked" class="mb-4" />

    <UCard v-if="!transactions?.data?.length" class="max-w-sm">
      <UIcon name="i-lucide-laptop-minimal-check" class="size-12 text-(--ui-text-muted)" />
      <p class="mt-2 text-pretty text-(--ui-text-muted)">
        В этом разделе пока пусто. У вас нет транзакций
      </p>
    </UCard>

    <UPageList class="space-y-2">
      <UPageCard
        v-for="(item, index) in transactions.data"
        :key="index"
        :ui="{ container: 'p-2 sm:p-4' }"
        variant="soft"
        orientation="horizontal"
      >
        <template #title>
          <UIcon
            v-if="item.type === 'deposit'"
            name="i-lucide-arrow-up-right"
            class="h-4 w-4 text-green-500"
          />
          <UIcon v-else name="i-lucide-arrow-down-right" class="h-4 w-4 text-red-500" />
          {{ $currency(item.amount) }}
        </template>
        <template #description>
          <div>
            <NuxtTime :datetime="item.date" date-style="long" time-style="short" />
            <div>
              <span v-if="item?.meta?.message">{{ item.meta.message }}</span>
              <span v-else-if="item?.meta?.title">{{ item.meta.title }}</span>
              <span v-else>{{ item.type === "deposit" ? "Пополнение" : "Списание" }}</span>
            </div>
          </div>
        </template>
      </UPageCard>
    </UPageList>

    <UPagination
      v-if="transactions?.meta?.last_page > 1"
      v-model:page="page"
      class="mt-4"
      :show-controls="false"
      :items-per-page="transactions.meta.per_page"
      :total="transactions.meta.total"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { vMaska } from "maska/vue";
import type { MaskInputOptions } from "maska";

const { refreshIdentity, user } = useSanctumAuth();
const { $currency } = useNuxtApp();
const client = useSanctumClient();
const page = ref(1);
const toast = useToast();
const amount = ref(0);
const amountInt = computed(() => amount.value);
const options = reactive<MaskInputOptions>({
  number: {
    locale: "ru",
    fraction: 2,
    unsigned: true
  },
  postProcess: (val) => (val ? `${val} ₽` : "")
});

const { data: transactions, refresh } = await useAsyncData(
  `transactions:${page.value}`,
  () =>
    client("/user/transactions", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);

const makeDeposit = async () => {
  if (amountInt.value <= 100) {
    return toast.add({
      id: "deposit-min-amount",
      title: "Минимальная сумма пополнения - 100₽",
      icon: "i-lucide-alert-triangle",
      color: "warning"
    });
  }

  await makeDepositBySum(amountInt.value);
};

const makeDepositBySum = async (sum: number) => {
  const response = await client("/user/deposit", {
    method: "POST",
    body: {
      amount: sum
    }
  });

  // Если есть url для оплаты — редиректим
  if (response?.url) {
    if (response?.message) {
      toast.add({
        id: "deposit-info",
        title: response.message,
        color: "info"
      });
    }
    window.location.href = response.url;
    return;
  }

  // Если есть сообщение об ошибке
  if (response?.message && response?.success === false) {
    return toast.add({
      id: "deposit-error",
      title: response.message,
      color: "warning"
    });
  }

  // Если успех
  if (response?.success) {
    await refresh();
    await refreshIdentity();
    amount.value = null;
    return toast.add({
      id: "deposit-success",
      title: "Ваш баланс успешно пополнен",
      color: "success"
    });
  }
};
</script>
