<template>
  <UContainer class="py-6">
    <div class="flex flex-col gap-6 lg:flex-row">
      <div class="w-full space-y-4 lg:w-1/4">
        <UCard>
          <template #header>
            <div class="flex flex-col items-center">
              <UAvatar v-bind="data.user?.avatar" size="xl" class="mb-4" />
              <h1 class="text-xl font-semibold">
                {{ data.user?.name }}
              </h1>
            </div>
          </template>

          <!-- <div class="space-y-4">
            <div class="flex flex-col gap-2">
              <UButton color="neutral" variant="outline" icon="i-lucide-mail" block>
                Написать сообщение
              </UButton>
            </div>
          </div> -->
        </UCard>

        <ItemIsShop v-if="data.user?.is_shop" />
      </div>

      <div class="w-full lg:w-3/4">
        <div ref="itemsContainer" class="mt-2 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
          <template v-if="data.data && status !== 'pending'">
            <CardItem v-for="(item, n) in data.data" :key="n" :item="item" />
          </template>

          <template v-if="status === 'pending'">
            <USkeleton v-for="i in 21" :key="i" class="h-64 w-full" />
          </template>
        </div>

        <Pagination
          :meta="data.meta"
          :page="page"
          :disabled="status === 'pending'"
          @set-page="page = $event"
        />
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page ? Number(route.query.page) : 1);

const { data, status } = await useAsyncData(
  `user:${route.params.id}`,
  () =>
    client(`/users/${route.params.id}`, {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);
</script>
