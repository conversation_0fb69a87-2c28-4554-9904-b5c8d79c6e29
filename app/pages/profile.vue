<template>
  <UDashboardPanel id="profile" :ui="{ body: 'lg:py-6' }">
    <template #header>
      <UDashboardNavbar title="Мои объявления">
        <template #right>
          <Search />
          <AuthUser />
          <UButton
            to="/add"
            color="error"
            class="hidden justify-center bg-red-800 hover:bg-red-900 lg:flex lg:min-w-40 dark:text-white"
          >
            Подать объявление
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="space-y-4 lg:max-w-2xl">
        <UTabs v-model="posts_status" :items="items" :content="false" class="w-full" />
      </div>

      <UCard
        v-if="!posts?.data?.length && posts_status === 'active' && status === 'success'"
        class="max-w-sm"
      >
        <UIcon name="i-lucide-layout-list" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)">
          В этом разделе пока пусто. Что бы добавить объявление нажмите кнопку ниже
        </p>
        <UButton
          to="/add"
          color="error"
          class="mt-4 justify-center bg-red-800 hover:bg-red-900 lg:min-w-40 dark:text-white"
        >
          Подать объявление
        </UButton>
      </UCard>

      <UCard
        v-if="!posts?.data?.length && posts_status === 'archived' && status === 'success'"
        class="max-w-sm"
      >
        <UIcon name="i-lucide-layout-list" class="size-12 text-(--ui-text-muted)" />
        <p class="mt-2 text-pretty text-(--ui-text-muted)">В этом разделе пока пусто</p>
      </UCard>

      <div class="space-y-4 lg:max-w-2xl">
        <div class="space-y-4">
          <div v-if="status === 'pending'" class="space-y-4">
            <USkeleton v-for="i in 5" :key="i" class="h-32 w-full" />
          </div>
          <div v-for="post in posts?.data" v-else :key="post.slug">
            <AdminRowCard :post="post" @refresh="refresh" />
          </div>

          <Pagination
            :meta="posts.meta"
            :page="page"
            :disabled="status === 'pending'"
            @set-page="page = $event"
          />

          <ModalsImportPost @refresh="refresh" />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
import type { TabsItem } from "@nuxt/ui";
import type { User } from "~/types/auth";

const client = useSanctumClient();
const route = useRoute();
const user = useSanctumUser<User>();
const { refreshIdentity } = useSanctumAuth();

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const items = ref<TabsItem[]>([
  {
    label: "Активные",
    value: "active"
  },
  {
    label: "Архив",
    value: "archived"
  }
]);

const page = ref(route.query.page || 1);
const posts_status = ref("active");

const {
  data: posts,
  status,
  refresh
} = await useAsyncData(
  `posts:user:${user.value?.id}:${page.value}`,
  () =>
    client("/user/posts", {
      params: {
        page: page.value,
        status: posts_status.value
      }
    }),
  {
    watch: [page, posts_status]
  }
);

onMounted(() => {
  refreshIdentity();
});
</script>
