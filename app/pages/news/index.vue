<template>
  <UContainer class="py-3 md:py-4">
    <UPageCard
      title="Новости охоты и оружия"
      description="Актуальные новости для охотников и владельцев оружия"
      variant="naked"
      orientation="horizontal"
      class="mb-4"
    >
      <template #title>
        <h1 class="text-xl leading-tight font-bold md:text-2xl">Новости охоты и оружия</h1>
      </template>
    </UPageCard>

    <template v-if="page === 1">
      <div class="grid w-full grid-cols-1 gap-3 md:grid-cols-3">
        <NuxtLink
          v-for="item in news.data.slice(0, 3)"
          :key="item.id"
          class="group"
          :to="{ name: 'news-slug', params: { slug: item.slug } }"
        >
          <div class="relative">
            <img
              v-if="item.cover"
              class="aspect-video w-full rounded-md object-cover"
              :src="item.cover"
              :alt="item.title"
            />
            <img
              v-else
              class="aspect-video w-full rounded-md object-cover"
              src="/images/post.jpg"
              :alt="item.title"
            />
            <p
              v-if="false"
              class="bg-opacity-50 absolute right-2 bottom-2 rounded-md bg-black px-1.5 text-sm font-medium text-white"
            >
              <NuxtTime v-if="item.published_at" :datetime="item.published_at" date-style="long" />
            </p>
          </div>
          <div class="p-3">
            <div class="space-y-2">
              <h3 class="line-clamp-2 text-base leading-snug font-semibold" :title="item.title">
                {{ item.title }}
              </h3>
              <p class="line-clamp-3 text-sm">
                {{ item.description }}
              </p>
              <NuxtTime
                :datetime="item.published_at"
                date-style="long"
                class="text-xs text-[var(--ui-text-muted)] md:text-sm"
              />
            </div>
          </div>
        </NuxtLink>
      </div>
      <USeparator type="dashed" class="my-4" />
      <div class="mt-4 grid gap-3 md:grid-cols-2">
        <NuxtLink
          v-for="item in news.data.slice(3)"
          :key="item.id"
          class="block"
          :to="{ name: 'news-slug', params: { slug: item.slug } }"
        >
          <div class="space-y-2">
            <h3 class="line-clamp-2 text-base leading-snug font-semibold" :title="item.title">
              {{ item.title }}
            </h3>
            <p class="line-clamp-3 text-sm">
              {{ item.description }}
            </p>
            <NuxtTime
              :datetime="item.published_at"
              date-style="long"
              class="text-xs text-[var(--ui-text-muted)] md:text-sm"
            />
          </div>
        </NuxtLink>
      </div>
    </template>

    <template v-else>
      <div class="mt-4 grid gap-3 md:grid-cols-2">
        <NuxtLink
          v-for="item in news.data"
          :key="item.id"
          class="block"
          :to="{ name: 'news-slug', params: { slug: item.slug } }"
        >
          <div class="space-y-2">
            <h3 class="line-clamp-2 text-base leading-snug font-semibold" :title="item.title">
              {{ item.title }}
            </h3>
            <p class="line-clamp-3 text-sm">
              {{ item.description }}
            </p>
            <NuxtTime
              :datetime="item.published_at"
              date-style="long"
              class="text-xs text-[var(--ui-text-muted)] md:text-sm"
            />
          </div>
        </NuxtLink>
      </div>
    </template>

    <Pagination
      :meta="news.meta"
      :disabled="status === 'pending'"
      class="mt-4"
      @set-page="page = $event"
    />
  </UContainer>
</template>

<script setup lang="ts">
import type { NewsResponse } from "~/types/news";

const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page ? Number(route.query.page) : 1);

const { data: news, status } = await useAsyncData<NewsResponse>(
  "news",
  () =>
    client("/news", {
      params: {
        ...route.query,
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);

if (route.query.page === "1") {
  useHead(() => ({
    link: [
      {
        rel: "canonical",
        href: "https://gunpost.ru/news"
      }
    ]
  }));
}

let title = "Новости охоты и оружия";
if (page.value > 1) {
  title += `. Стр. ${page.value}`;
}

useSeoMeta({
  title: title,
  ogTitle: title,
  description: "Актуальные новости для охотников и владельцев оружия",
  ogDescription: "Актуальные новости для охотников и владельцев оружия"
});
</script>
