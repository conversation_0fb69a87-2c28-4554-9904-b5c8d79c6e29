<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-950">
    <UContainer class="py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Start a new discussion
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Ask questions, share ideas, and connect with the community
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Form -->
        <div class="lg:col-span-2">
          <UCard class="overflow-visible">
            <template #header>
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                  Create Discussion
                </h2>
                <UBadge color="green" variant="subtle">
                  <UIcon name="i-heroicons-sparkles" class="w-3 h-3 mr-1" />
                  New
                </UBadge>
              </div>
            </template>

            <form @submit.prevent="handleSubmit" class="space-y-6">
              <!-- Category Selection -->
              <UFormGroup label="Category" required>
                <USelectMenu
                  v-model="formData.category"
                  :options="categories"
                  placeholder="Select a category"
                  value-attribute="id"
                  option-attribute="name"
                >
                  <template #label>
                    <span v-if="formData.category">
                      <UIcon :name="getCategoryIcon(formData.category)" class="w-4 h-4 mr-2" />
                      {{ getCategoryName(formData.category) }}
                    </span>
                  </template>
                  <template #option="{ option }">
                    <div class="flex items-center gap-2">
                      <UIcon :name="option.icon" class="w-4 h-4 flex-shrink-0" :class="option.color" />
                      <div class="flex-1">
                        <p class="text-sm font-medium">{{ option.name }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ option.description }}</p>
                      </div>
                    </div>
                  </template>
                </USelectMenu>
              </UFormGroup>

              <!-- Title -->
              <UFormGroup label="Title" required>
                <UInput
                  v-model="formData.title"
                  placeholder="Enter a descriptive title for your discussion"
                  size="lg"
                  :ui="{ base: 'font-medium' }"
                />
              </UFormGroup>

              <!-- Tags -->
              <UFormGroup label="Tags" description="Add up to 5 tags to help others find your discussion">
                <div class="space-y-2">
                  <div class="flex flex-wrap gap-2">
                    <UBadge
                      v-for="tag in formData.tags"
                      :key="tag"
                      color="gray"
                      variant="solid"
                      class="px-3 py-1"
                    >
                      {{ tag }}
                      <UButton
                        color="gray"
                        variant="ghost"
                        size="2xs"
                        :padded="false"
                        @click="removeTag(tag)"
                        class="ml-1 -mr-1"
                      >
                        <UIcon name="i-heroicons-x-mark-20-solid" class="w-3 h-3" />
                      </UButton>
                    </UBadge>
                  </div>
                  <div class="flex gap-2">
                    <UInput
                      v-model="newTag"
                      placeholder="Add a tag..."
                      @keyup.enter="addTag"
                      :disabled="formData.tags.length >= 5"
                    />
                    <UButton
                      @click="addTag"
                      color="gray"
                      variant="solid"
                      :disabled="!newTag || formData.tags.length >= 5"
                    >
                      Add Tag
                    </UButton>
                  </div>
                </div>
              </UFormGroup>

              <!-- Content Editor -->
              <UFormGroup label="Content" required>
                <UCard :ui="{ body: { padding: '' } }">
                  <!-- Editor Toolbar -->
                  <div class="border-b border-gray-200 dark:border-gray-800 px-4 py-2">
                    <div class="flex items-center gap-1">
                      <UButtonGroup size="sm" orientation="horizontal">
                        <UButton color="gray" variant="ghost" icon="i-heroicons-bold" />
                        <UButton color="gray" variant="ghost" icon="i-heroicons-italic" />
                        <UButton color="gray" variant="ghost" icon="i-heroicons-link" />
                      </UButtonGroup>
                      <UDivider orientation="vertical" class="mx-2 h-6" />
                      <UButtonGroup size="sm" orientation="horizontal">
                        <UButton color="gray" variant="ghost" icon="i-heroicons-list-bullet" />
                        <UButton color="gray" variant="ghost" icon="i-heroicons-numbered-list" />
                        <UButton color="gray" variant="ghost" icon="i-heroicons-code-bracket" />
                      </UButtonGroup>
                      <UDivider orientation="vertical" class="mx-2 h-6" />
                      <UButton color="gray" variant="ghost" size="sm" icon="i-heroicons-photo" />
                      <UButton color="gray" variant="ghost" size="sm" icon="i-heroicons-face-smile" />
                    </div>
                  </div>

                  <!-- Text Area -->
                  <UTextarea
                    v-model="formData.content"
                    placeholder="Write your discussion content here. You can use Markdown for formatting..."
                    :rows="12"
                    :ui="{
                      base: 'font-mono text-sm',
                      padding: { default: 'px-4 py-3' },
                      rounded: 'rounded-none',
                      border: { base: 'border-0' }
                    }"
                  />

                  <!-- Editor Footer -->
                  <div class="border-t border-gray-200 dark:border-gray-800 px-4 py-2">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>Markdown supported</span>
                        <button type="button" class="hover:text-primary-500 transition">
                          Preview
                        </button>
                      </div>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {{ formData.content.length }} characters
                      </span>
                    </div>
                  </div>
                </UCard>
              </UFormGroup>

              <!-- Poll Option -->
              <UFormGroup>
                <UCheckbox v-model="formData.hasPoll" label="Add a poll to this discussion" />
                <Transition name="slide-down">
                  <div v-if="formData.hasPoll" class="mt-4 space-y-3">
                    <UInput
                      v-model="pollQuestion"
                      placeholder="Poll question..."
                      class="font-medium"
                    />
                    <div class="space-y-2">
                      <div v-for="(option, index) in pollOptions" :key="index" class="flex gap-2">
                        <UInput
                          v-model="pollOptions[index]"
                          :placeholder="`Option ${index + 1}`"
                        />
                        <UButton
                          v-if="pollOptions.length > 2"
                          color="gray"
                          variant="ghost"
                          icon="i-heroicons-trash"
                          @click="pollOptions.splice(index, 1)"
                        />
                      </div>
                      <UButton
                        color="gray"
                        variant="soft"
                        icon="i-heroicons-plus"
                        size="sm"
                        @click="pollOptions.push('')"
                        block
                      >
                        Add option
                      </UButton>
                    </div>
                  </div>
                </Transition>
              </UFormGroup>

              <!-- Actions -->
              <div class="flex items-center justify-between pt-4">
                <div class="flex items-center gap-2">
                  <UToggle v-model="formData.isPublic" />
                  <span class="text-sm text-gray-600 dark:text-gray-400">
                    Make public
                  </span>
                </div>
                <div class="flex items-center gap-3">
                  <UButton color="gray" variant="ghost" size="lg">
                    Save as draft
                  </UButton>
                  <UButton
                    type="submit"
                    color="primary"
                    size="lg"
                    icon="i-heroicons-paper-airplane"
                    :loading="isSubmitting"
                  >
                    Start Discussion
                  </UButton>
                </div>
              </div>
            </form>
          </UCard>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Guidelines Card -->
          <UCard>
            <template #header>
              <div class="flex items-center gap-2">
                <UIcon name="i-heroicons-light-bulb" class="w-5 h-5 text-yellow-500" />
                <h3 class="font-semibold">Discussion Guidelines</h3>
              </div>
            </template>
            <ul class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
              <li class="flex items-start gap-2">
                <UIcon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Be respectful and constructive</span>
              </li>
              <li class="flex items-start gap-2">
                <UIcon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Search before posting duplicates</span>
              </li>
              <li class="flex items-start gap-2">
                <UIcon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Use clear and descriptive titles</span>
              </li>
              <li class="flex items-start gap-2">
                <UIcon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Provide context and examples</span>
              </li>
            </ul>
          </UCard>

          <!-- Recent Discussions -->
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="font-semibold">Recent Discussions</h3>
                <UBadge color="gray" variant="subtle">24h</UBadge>
              </div>
            </template>
            <div class="space-y-3">
              <div v-for="discussion in recentDiscussions" :key="discussion.id" class="group">
                <a href="#" class="block hover:bg-gray-50 dark:hover:bg-gray-900 -mx-2 px-2 py-2 rounded-md transition">
                  <div class="flex items-start gap-3">
                    <UAvatar :src="discussion.author.avatar" :alt="discussion.author.name" size="xs" />
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 dark:text-white truncate group-hover:text-primary-500 transition">
                        {{ discussion.title }}
                      </p>
                      <div class="flex items-center gap-2 mt-1">
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                          {{ discussion.author.name }}
                        </span>
                        <span class="text-xs text-gray-400 dark:text-gray-500">•</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                          {{ discussion.time }}
                        </span>
                      </div>
                    </div>
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                      <UIcon name="i-heroicons-chat-bubble-left-ellipsis" class="w-3 h-3" />
                      <span>{{ discussion.replies }}</span>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </UCard>

          <!-- Popular Tags -->
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="font-semibold">Popular Tags</h3>
                <UIcon name="i-heroicons-fire" class="w-4 h-4 text-orange-500" />
              </div>
            </template>
            <div class="flex flex-wrap gap-2">
              <UBadge
                v-for="tag in popularTags"
                :key="tag.name"
                color="gray"
                variant="soft"
                class="cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 transition"
              >
                {{ tag.name }}
                <span class="ml-1 text-xs opacity-60">{{ tag.count }}</span>
              </UBadge>
            </div>
          </UCard>
        </div>
      </div>
    </UContainer>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Form data
const formData = ref({
  category: '',
  title: '',
  tags: [],
  content: '',
  hasPoll: false,
  isPublic: true
})

const newTag = ref('')
const isSubmitting = ref(false)
const pollQuestion = ref('')
const pollOptions = ref(['', ''])

// Categories
const categories = [
  {
    id: 'general',
    name: 'General',
    icon: 'i-heroicons-chat-bubble-left-right',
    color: 'text-blue-500',
    description: 'General discussions and conversations'
  },
  {
    id: 'ideas',
    name: 'Ideas',
    icon: 'i-heroicons-light-bulb',
    color: 'text-yellow-500',
    description: 'Share your ideas and suggestions'
  },
  {
    id: 'q-and-a',
    name: 'Q&A',
    icon: 'i-heroicons-question-mark-circle',
    color: 'text-purple-500',
    description: 'Ask the community for help'
  },
  {
    id: 'show-and-tell',
    name: 'Show and Tell',
    icon: 'i-heroicons-sparkles',
    color: 'text-green-500',
    description: 'Share your projects and creations'
  },
  {
    id: 'polls',
    name: 'Polls',
    icon: 'i-heroicons-chart-bar',
    color: 'text-orange-500',
    description: 'Create polls and gather opinions'
  }
]

// Recent discussions mock data
const recentDiscussions = [
  {
    id: 1,
    title: 'How to optimize Nuxt 3 performance?',
    author: {
      name: 'Alex Chen',
      avatar: 'https://avatars.githubusercontent.com/u/1?v=4'
    },
    time: '2 hours ago',
    replies: 12
  },
  {
    id: 2,
    title: 'Best practices for TypeScript in Vue 3',
    author: {
      name: 'Sarah Miller',
      avatar: 'https://avatars.githubusercontent.com/u/2?v=4'
    },
    time: '5 hours ago',
    replies: 8
  },
  {
    id: 3,
    title: 'Introducing my new UI component library',
    author: {
      name: 'John Doe',
      avatar: 'https://avatars.githubusercontent.com/u/3?v=4'
    },
    time: '1 day ago',
    replies: 24
  }
]

// Popular tags mock data
const popularTags = [
  { name: 'vue3', count: 156 },
  { name: 'nuxt3', count: 143 },
  { name: 'typescript', count: 98 },
  { name: 'performance', count: 87 },
  { name: 'ui-components', count: 76 },
  { name: 'best-practices', count: 65 },
  { name: 'tutorial', count: 54 },
  { name: 'help', count: 43 }
]

// Helper functions
const getCategoryIcon = (categoryId) => {
  const category = categories.find(c => c.id === categoryId)
  return category ? category.icon : ''
}

const getCategoryName = (categoryId) => {
  const category = categories.find(c => c.id === categoryId)
  return category ? category.name : ''
}

const addTag = () => {
  if (newTag.value && formData.value.tags.length < 5 && !formData.value.tags.includes(newTag.value)) {
    formData.value.tags.push(newTag.value.toLowerCase())
    newTag.value = ''
  }
}

const removeTag = (tag) => {
  formData.value.tags = formData.value.tags.filter(t => t !== tag)
}

const handleSubmit = async () => {
  isSubmitting.value = true
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 2000))
  isSubmitting.value = false

  console.log('Form submitted:', {
    ...formData.value,
    poll: formData.value.hasPoll ? {
      question: pollQuestion.value,
      options: pollOptions.value.filter(o => o)
    } : null
  })
}
</script>

<style scoped>
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-10px);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
</style>
