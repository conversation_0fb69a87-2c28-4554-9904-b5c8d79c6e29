<template>
  <div class="min-h-screen ">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
      <UContainer class="py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Оружейные форумы
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
              Join the discussion with over 50,000 developers worldwide
            </p>
          </div>
          <div v-if="false" class="flex items-center gap-3">
            <UButton color="white" icon="i-heroicons-magnifying-glass" size="lg">
              Search
            </UButton>
            <UButton color="primary" icon="i-heroicons-plus" size="lg">
              New Discussion
            </UButton>
          </div>
        </div>

        <!-- Stats Bar -->
        <div v-if="false" class="grid grid-cols-4 gap-6 mt-8">
          <div class="flex items-center gap-3">
            <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <UIcon name="i-heroicons-user-group" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">52,841</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Members</p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <UIcon name="i-heroicons-chat-bubble-left-right" class="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">12,453</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Discussions</p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <UIcon name="i-heroicons-chat-bubble-bottom-center-text" class="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">98,234</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Replies</p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <div class="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <UIcon name="i-heroicons-fire" class="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">1,234</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Online Now</p>
            </div>
          </div>
        </div>
      </UContainer>
    </div>

    <UContainer class="py-4">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content - Categories -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Announcements Section -->
          <UCard class="overflow-hidden border-2 border-primary-500/20 bg-gradient-to-r from-primary-50 to-transparent dark:from-primary-950/20 dark:to-transparent">
            <div class="flex items-start gap-4">
              <div class="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
                <UIcon name="i-heroicons-megaphone" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div class="flex-1">
                <div class="flex items-center justify-between mb-2">
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">
                    Announcements
                  </h2>
                  <UBadge color="primary" variant="subtle">
                    <UIcon name="i-heroicons-star" class="w-3 h-3 mr-1" />
                    Pinned
                  </UBadge>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Important updates and announcements from the team
                </p>
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-6 text-sm">
                    <span class="text-gray-500 dark:text-gray-400">
                      <strong class="text-gray-900 dark:text-white">5</strong> topics
                    </span>
                    <span class="text-gray-500 dark:text-gray-400">
                      <strong class="text-gray-900 dark:text-white">23</strong> posts
                    </span>
                  </div>
                  <div class="flex items-center gap-2">
                    <UUser size="sm" :avatar="{ src: 'https://avatars.githubusercontent.com/u/1?v=4' }" name="New UI Update Released" description="2 hours ago by Admin" />
                  </div>
                </div>
              </div>
            </div>
          </UCard>

          <!-- Main Categories -->
          <div v-for="category in categories" :key="category.id" class="space-y-4">
            <!-- Category Header -->
            <div class="flex items-center gap-3 mb-3">
              <div :class="category.iconBg" class="p-2 rounded-lg">
                <UIcon :name="category.icon" :class="category.iconColor" class="w-5 h-5" />
              </div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                {{ category.name }}
              </h2>
            </div>

            <!-- Subcategories -->
            <div class="space-y-3">
              <UCard
                v-for="subcategory in category.subcategories"
                :key="subcategory.id"
                class="hover:shadow-lg transition-all duration-200 hover:scale-[1.01] cursor-pointer"
                @click="navigateToSubcategory(subcategory.id)"
              >
                <div class="flex items-start gap-4">
                  <!-- Subcategory Content -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-2">
                      <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white hover:text-primary-500 transition">
                          {{ subcategory.name }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {{ subcategory.description }}
                        </p>
                      </div>
                      <div v-if="subcategory.isNew" class="flex-shrink-0">
                        <UBadge color="green" variant="subtle" size="xs">
                          NEW
                        </UBadge>
                      </div>
                    </div>

                    <!-- Stats and Latest Post -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-4 text-sm">
                        <span class="flex items-center gap-1 text-gray-500 dark:text-gray-400">
                          <UIcon name="i-heroicons-document-text" class="w-4 h-4" />
                          <strong class="text-gray-900 dark:text-white">{{ subcategory.topics }}</strong>
                          topics
                        </span>
                        <span class="flex items-center gap-1 text-gray-500 dark:text-gray-400">
                          <UIcon name="i-heroicons-chat-bubble-left" class="w-4 h-4" />
                          <strong class="text-gray-900 dark:text-white">{{ subcategory.posts }}</strong>
                          posts
                        </span>
                        <UUser size="xs" :avatar="{ src: subcategory.latestPost.avatar }" :name="`${subcategory.latestPost.time} - ${subcategory.latestPost.author}`" :description="subcategory.latestPost.title" />
                      </div>
                    </div>
                  </div>
                </div>
              </UCard>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Quick Links -->
          <UCard>
            <template #header>
              <div class="flex items-center gap-2">
                <UIcon name="i-heroicons-bolt" class="w-5 h-5 text-yellow-500" />
                <h3 class="font-semibold">Quick Links</h3>
              </div>
            </template>
            <div class="space-y-2">
              <UButton variant="ghost" color="gray" class="w-full justify-start" icon="i-heroicons-book-open">
                Forum Rules
              </UButton>
              <UButton variant="ghost" color="gray" class="w-full justify-start" icon="i-heroicons-academic-cap">
                Getting Started Guide
              </UButton>
              <UButton variant="ghost" color="gray" class="w-full justify-start" icon="i-heroicons-trophy">
                Leaderboard
              </UButton>
              <UButton variant="ghost" color="gray" class="w-full justify-start" icon="i-heroicons-tag">
                All Tags
              </UButton>
            </div>
          </UCard>

          <!-- Trending Discussions -->
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <UIcon name="i-heroicons-arrow-trending-up" class="w-5 h-5 text-green-500" />
                  <h3 class="font-semibold">Trending Now</h3>
                </div>
                <UBadge color="red" variant="subtle" size="xs">
                  HOT
                </UBadge>
              </div>
            </template>
            <div class="space-y-3">
              <div v-for="(topic, index) in trendingTopics" :key="topic.id" class="group">
                <a href="#" class="flex items-start gap-3 hover:bg-gray-50 dark:hover:bg-gray-900 -mx-2 px-2 py-2 rounded-md transition">
                  <span class="text-lg font-bold text-gray-400 dark:text-gray-600 mt-0.5">
                    {{ index + 1 }}
                  </span>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-primary-500 transition line-clamp-2">
                      {{ topic.title }}
                    </p>
                    <div class="flex items-center gap-3 mt-1 text-xs text-gray-500 dark:text-gray-400">
                      <span class="flex items-center gap-1">
                        <UIcon name="i-heroicons-arrow-up" class="w-3 h-3 text-green-500" />
                        {{ topic.votes }}
                      </span>
                      <span class="flex items-center gap-1">
                        <UIcon name="i-heroicons-chat-bubble-left" class="w-3 h-3" />
                        {{ topic.replies }}
                      </span>
                      <span class="flex items-center gap-1">
                        <UIcon name="i-heroicons-eye" class="w-3 h-3" />
                        {{ topic.views }}
                      </span>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </UCard>

          <!-- Active Users -->
          <UCard>
            <template #header>
              <div class="flex items-center gap-2">
                <span class="relative flex h-3 w-3">
                  <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  <span class="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                </span>
                <h3 class="font-semibold">Active Contributors</h3>
              </div>
            </template>
            <div class="space-y-3">
              <div v-for="user in activeUsers" :key="user.id" class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <UAvatar :src="user.avatar" :alt="user.name" size="xs" />
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ user.name }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ user.role }}
                    </p>
                  </div>
                </div>
                <UBadge :color="user.badgeColor" variant="subtle" size="xs">
                  {{ user.badge }}
                </UBadge>
              </div>
            </div>
          </UCard>

          <!-- Forum Stats -->
          <UCard>
            <template #header>
              <div class="flex items-center gap-2">
                <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-blue-500" />
                <h3 class="font-semibold">Forum Statistics</h3>
              </div>
            </template>
            <div class="space-y-4">
              <div>
                <div class="flex items-center justify-between mb-1">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Daily Activity</span>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">89%</span>
                </div>
                <UProgress :value="89" color="primary" size="sm" />
              </div>
              <div>
                <div class="flex items-center justify-between mb-1">
                  <span class="text-sm text-gray-600 dark:text-gray-400">Response Rate</span>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">94%</span>
                </div>
                <UProgress :value="94" color="green" size="sm" />
              </div>
              <div>
                <div class="flex items-center justify-between mb-1">
                  <span class="text-sm text-gray-600 dark:text-gray-400">User Satisfaction</span>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">97%</span>
                </div>
                <UProgress :value="97" color="orange" size="sm" />
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </UContainer>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Categories data
const categories = ref([
  {
    id: 'development',
    name: 'Производители Оружия',
    icon: 'i-heroicons-code-bracket',
    iconBg: 'bg-blue-100 dark:bg-blue-900/30',
    iconColor: 'text-blue-600 dark:text-blue-400',
    subcategories: [
      {
        id: 'frontend',
        name: 'Общение с компаниями',
        description: 'для тех у кого пока нет собственного раздела',
        topics: 2341,
        posts: 15234,
        online: 123,
        isNew: false,
        latestPost: {
          title: 'Травматический GLOCK от custom-guns',
          author: 'reerii',
          avatar: 'https://forum.guns.ru/forums/icons/users/658065.jpg',
          time: '5 min ago'
        }
      },
      {
        id: 'backend',
        name: 'Концерн "Калашников"',
        description: 'Node.js, Python, databases, APIs, and server-side programming',
        topics: 1892,
        posts: 12453,
        online: 89,
        latestPost: {
          title: 'Scaling Node.js applications with PM2',
          author: 'DevGuru',
          avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
          time: '12 min ago'
        }
      },
      {
        id: 'mobile',
        name: 'Продукция завода Молот',
        description: 'iOS, Android, React Native, Flutter, and cross-platform development',
        topics: 923,
        posts: 6234,
        online: 45,
        isNew: true,
        latestPost: {
          title: 'Flutter 3.0: What\'s new?',
          author: 'MobileFirst',
          avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
          time: '1 hour ago'
        }
      },
      {
        id: '1mobile',
        name: 'Продукция Златоустовского Машиностроительного Завода',
        description: 'iOS, Android, React Native, Flutter, and cross-platform development',
        topics: 923,
        posts: 6234,
        online: 45,
        isNew: true,
        latestPost: {
          title: 'Flutter 3.0: What\'s new?',
          author: 'MobileFirst',
          avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
          time: '1 hour ago'
        }
      },
      {
        id: '2mobile',
        name: 'Продукция ООО ПКП АКБС',
        description: 'iOS, Android, React Native, Flutter, and cross-platform development',
        topics: 923,
        posts: 6234,
        online: 45,
        isNew: true,
        latestPost: {
          title: 'Flutter 3.0: What\'s new?',
          author: 'MobileFirst',
          avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
          time: '1 hour ago'
        }
      }
    ]
  },
  {
    id: 'design',
    name: 'Design & UX',
    icon: 'i-heroicons-paint-brush',
    iconBg: 'bg-pink-100 dark:bg-pink-900/30',
    iconColor: 'text-pink-600 dark:text-pink-400',
    subcategories: [
      {
        id: 'ui-design',
        name: 'UI Design',
        description: 'User interface design, design systems, and visual design principles',
        icon: 'i-heroicons-swatch',
        iconBg: 'bg-indigo-100 dark:bg-indigo-900/30',
        iconColor: 'text-indigo-600 dark:text-indigo-400',
        topics: 734,
        posts: 4532,
        online: 34,
        tags: ['figma', 'design-systems', 'components'],
        latestPost: {
          title: 'Creating accessible color palettes',
          author: 'DesignPro',
          avatar: 'https://avatars.githubusercontent.com/u/4?v=4',
          time: '30 min ago'
        }
      },
      {
        id: 'ux-research',
        name: 'UX Research',
        description: 'User research, usability testing, and experience optimization',
        icon: 'i-heroicons-chart-pie',
        iconBg: 'bg-cyan-100 dark:bg-cyan-900/30',
        iconColor: 'text-cyan-600 dark:text-cyan-400',
        topics: 423,
        posts: 2341,
        online: 23,
        tags: ['user-testing', 'analytics', 'a-b-testing'],
        latestPost: {
          title: 'Conducting remote user interviews',
          author: 'UXExpert',
          avatar: 'https://avatars.githubusercontent.com/u/5?v=4',
          time: '2 hours ago'
        }
      }
    ]
  },
  {
    id: 'community',
    name: 'Community',
    icon: 'i-heroicons-user-group',
    iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
    subcategories: [
      {
        id: 'showcase',
        name: 'Project Showcase',
        description: 'Share your projects, get feedback, and inspire others',
        icon: 'i-heroicons-sparkles',
        iconBg: 'bg-emerald-100 dark:bg-emerald-900/30',
        iconColor: 'text-emerald-600 dark:text-emerald-400',
        topics: 1234,
        posts: 8923,
        online: 67,
        tags: ['portfolio', 'feedback', 'open-source'],
        latestPost: {
          title: 'Launched my SaaS after 6 months!',
          author: 'Maker',
          avatar: 'https://avatars.githubusercontent.com/u/6?v=4',
          time: '15 min ago'
        }
      },
      {
        id: 'career',
        name: 'Career & Jobs',
        description: 'Career advice, job opportunities, and professional development',
        icon: 'i-heroicons-briefcase',
        iconBg: 'bg-rose-100 dark:bg-rose-900/30',
        iconColor: 'text-rose-600 dark:text-rose-400',
        topics: 892,
        posts: 5234,
        online: 45,
        isNew: true,
        tags: ['remote', 'hiring', 'freelance', 'resume'],
        latestPost: {
          title: 'How I landed a job at FAANG',
          author: 'TechRecruiter',
          avatar: 'https://avatars.githubusercontent.com/u/7?v=4',
          time: '45 min ago'
        }
      },
      {
        id: 'off-topic',
        name: 'Off Topic',
        description: 'General discussions, random thoughts, and community bonding',
        icon: 'i-heroicons-chat-bubble-left-ellipsis',
        iconBg: 'bg-gray-100 dark:bg-gray-900/30',
        iconColor: 'text-gray-600 dark:text-gray-400',
        topics: 3421,
        posts: 23456,
        online: 234,
        tags: ['random', 'fun', 'general'],
        latestPost: {
          title: 'What\'s your dev setup?',
          author: 'CuriousDev',
          avatar: 'https://avatars.githubusercontent.com/u/8?v=4',
          time: '3 min ago'
        }
      }
    ]
  }
])

// Trending topics
const trendingTopics = ref([
  {
    id: 1,
    title: 'Nuxt 3.13 released with major performance improvements',
    votes: 234,
    replies: 89,
    views: '2.3k'
  },
  {
    id: 2,
    title: 'Is TypeScript really worth it for small projects?',
    votes: 189,
    replies: 142,
    views: '5.1k'
  },
  {
    id: 3,
    title: 'My journey from Junior to Senior Developer in 2 years',
    votes: 156,
    replies: 67,
    views: '3.8k'
  },
  {
    id: 4,
    title: 'The best VS Code extensions you\'re not using',
    votes: 134,
    replies: 45,
    views: '1.9k'
  },
  {
    id: 5,
    title: 'Why I switched from React to Vue and never looked back',
    votes: 121,
    replies: 234,
    views: '8.2k'
  }
])

// Active users
const activeUsers = ref([
  {
    id: 1,
    name: 'Sarah Chen',
    role: 'Moderator',
    avatar: 'https://avatars.githubusercontent.com/u/10?v=4',
    badge: 'PRO',
    badgeColor: 'primary'
  },
  {
    id: 2,
    name: 'Alex Rivera',
    role: 'Top Contributor',
    avatar: 'https://avatars.githubusercontent.com/u/11?v=4',
    badge: 'EXPERT',
    badgeColor: 'orange'
  },
  {
    id: 3,
    name: 'Michael Park',
    role: 'Developer',
    avatar: 'https://avatars.githubusercontent.com/u/12?v=4',
    badge: 'ACTIVE',
    badgeColor: 'green'
  },
  {
    id: 4,
    name: 'Emma Wilson',
    role: 'Designer',
    avatar: 'https://avatars.githubusercontent.com/u/13?v=4',
    badge: 'NEW',
    badgeColor: 'purple'
  }
])

// Navigation function
const navigateToSubcategory = (subcategoryId) => {
  console.log('Navigating to subcategory:', subcategoryId)
  // Implement navigation logic here
}
</script>

<style scoped>
/* Custom animations */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-dot {
  animation: pulse-dot 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth hover transitions */
.group:hover .group-hover\:text-primary-500 {
  transition: color 0.2s ease;
}

/* Line clamp utility */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
