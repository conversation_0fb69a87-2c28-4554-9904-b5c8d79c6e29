<template>
  <UContainer class="mt-4">
    <div class="flex items-center gap-2">
      <UButton to="/events" size="xs" color="neutral" variant="outline" icon="i-lucide-arrow-left"
        >Назад</UButton
      >

      <span v-if="data.data?.start_date" class="text-sm text-[var(--ui-text-muted)]">
        {{ data.data?.start_date }}
      </span>
      <span v-if="data.data?.end_date" class="text-sm text-[var(--ui-text-muted)]">
        - {{ data.data?.end_date }}
      </span>
    </div>

    <UPageCard :title="data.data?.title" variant="naked" orientation="horizontal" class="mt-1 mb-4">
      <template #title>
        <h1 class="text-2xl font-bold">{{ data.data?.title }}</h1>
      </template>
    </UPageCard>

    <div class="flex flex-col gap-4 lg:flex-row">
      <div class="w-full">
        <NewsPost :news="data" />
      </div>
      <div class="w-full space-y-4 lg:w-110"></div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const route = useRoute();

const { data } = await useAsyncData(`events:${route.params.slug}`, () =>
  client(`/events/${route.params.slug}`)
);

useSeoMeta({
  title: data.value?.data?.meta_title || news.value?.data?.title,
  ogTitle: data.value?.data?.meta_title || news.value?.data?.title,
  description: data.value?.data?.meta_description || news.value?.data?.description,
  ogDescription: data.value?.data?.meta_description || news.value?.data?.description,
  ogImage: data.value?.data?.image,
  ogLocale: "ru"
});
</script>
