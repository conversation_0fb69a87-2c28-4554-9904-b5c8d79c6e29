<template>
  <UDashboardPanel id="settings" :ui="{ body: 'lg:p-0 p-0 sm:p-0' }">
    <template #header>
      <UDashboardNavbar title="Сообщения">
        <template #right>
          <Search />
          <AuthUser />
          <UButton
            to="/add"
            color="error"
            class="hidden justify-center bg-red-800 hover:bg-red-900 lg:flex lg:min-w-40 dark:text-white"
          >
            Подать объявление
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="flex w-full flex-col gap-4 sm:gap-6 lg:max-w-2xl lg:gap-12">
        <NuxtPage />
      </div>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});
</script>
