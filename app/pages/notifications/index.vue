<template>
  <div>
    <UPageCard title="Ваши уведомления" variant="naked" class="mb-4" />

    <UCard v-if="!notifications?.data?.length" class="max-w-sm">
      <UIcon name="i-lucide-laptop-minimal-check" class="size-12 text-(--ui-text-muted)" />
      <p class="mt-2 text-pretty text-(--ui-text-muted)">
        В этом разделе пока пусто. У вас нет уведомлений
      </p>
    </UCard>

    <UPageList class="space-y-2">
      <UPageCard
        v-for="(item, index) in notifications.data"
        :key="index"
        :ui="{ container: 'p-2 sm:p-4' }"
        variant="soft"
        orientation="horizontal"
        :title="item.title"
        :description="item.message"
        :to="item?.to"
        :class="[item.read_at ? 'cursor-default' : 'cursor-pointer']"
        @click="markRead(item)"
      >
        <template #leading>
          <div class="flex flex-wrap items-center gap-1.5">
            <UIcon
              v-if="isLoading === item.id"
              name="i-lucide-loader-circle"
              class="h-4 w-4 animate-spin"
            />
            <UChip v-if="!item.read_at" standalone inset />
            <span v-if="item?.icon">
              {{ item.icon }}
            </span>
            <UAvatar v-if="item?.avatar" v-bind="item.avatar" />
            <NuxtTime
              class="text-sm text-[var(--ui-text-muted)]"
              :datetime="item.created_at"
              date-style="long"
              time-style="short"
              :relative="false"
            />
          </div>
        </template>
      </UPageCard>
    </UPageList>

    <UPagination
      v-if="notifications?.meta?.last_page > 1"
      v-model:page="page"
      class="mt-4"
      :show-controls="false"
      :items-per-page="notifications.meta.per_page"
      :total="notifications.meta.total"
    />
  </div>
</template>

<script setup lang="ts">
const client = useSanctumClient();
const { refreshIdentity } = useSanctumAuth();

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const page = ref(1);
const isLoading = ref(null);

const { data: notifications, refresh } = await useAsyncData(
  `notifications:${page.value}`,
  () =>
    client("/user/notifications", {
      params: {
        page: page.value
      }
    }),
  {
    watch: [page]
  }
);

const markRead = async (item) => {
  if (item.read_at) return;
  isLoading.value = item.id;
  await client(`/user/notifications/${item.id}`);
  await refresh();
  await refreshIdentity();

  isLoading.value = null;
};
</script>
