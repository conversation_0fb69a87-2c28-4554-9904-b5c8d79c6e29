<template>
  <div class="space-y-4">
    <UForm id="settings" ref="form" :state="profile" @submit="onSubmit">
      <UPageCard
        title="Ваш профиль"
        description="Эта информация будет доступна публично"
        variant="naked"
        orientation="horizontal"
        class="mb-4"
      >
        <UButton
          :loading="isLoading"
          form="settings"
          label="Сохранить"
          type="submit"
          class="w-fit lg:ms-auto"
        />
      </UPageCard>

      <UAlert
        v-if="hasChanges"
        color="info"
        variant="outline"
        title="Сохраните свои изменения"
        description="Ваш профиль не сохранен. Нажмите кнопку 'Сохранить' страницы, чтобы сохранить изменения."
        icon="i-lucide-triangle-alert"
        class="mb-4"
      />

      <UPageCard variant="subtle" class="mb-4">
        <UFormField
          name="color"
          label="Уведомления"
          description="Получать уведомления в Telegram"
          class="flex items-start justify-between gap-4 max-sm:flex-col"
        >
          <UBadge v-if="user?.telegram_notification" size="xl" color="success" variant="soft"
            >Подключены</UBadge
          >
          <UButton v-else @click="authTelegram">Добавить Telegram</UButton>
        </UFormField>
      </UPageCard>

      <UPageCard variant="subtle">
        <UFormField
          name="theme"
          label="Тема"
          description="Выберите тему для вашего аккаунта"
          class="flex items-start justify-between gap-4 max-sm:flex-col"
        >
          <ColorModeSelect v-model="profile.theme" />
        </UFormField>
        <USeparator v-if="profile.email" />
        <UFormField
          name="name"
          label="Ваше имя"
          description="Будет отображаться в объявлении, уведомлениях и других сообщениях"
          required
          class="flex items-start justify-between gap-4 max-sm:flex-col"
        >
          <UInput v-model="profile.name" autocomplete="off" />
        </UFormField>
        <USeparator v-if="profile.email" />
        <UFormField
          v-if="profile.email"
          name="email"
          label="Ваша почта"
          description="Используется для входа в систему, чеков и обновлений"
          required
          class="flex items-start justify-between gap-4 max-sm:flex-col"
        >
          <span class="text-md font-semibold">{{ profile.email }}</span>
        </UFormField>
        <USeparator />
        <UFormField
          name="phone"
          label="Ваш телефон"
          description="Используется для входа в систему и уведомлений"
          placeholder="****** 111 22 33"
          class="flex items-start justify-between gap-4 max-sm:flex-col"
        >
          <span class="text-md font-semibold">{{ profile.phone }}</span>
        </UFormField>
        <USeparator />
        <UFormField
          name="avatar"
          label="Ваш аватар"
          description="JPG, GIF или PNG. До 1MB."
          class="flex justify-between gap-4 max-sm:flex-col sm:items-center"
        >
          <div class="flex flex-wrap items-center gap-3">
            <UAvatar :src="profile.avatar_preview" :alt="profile.name" size="lg" />
            <UButton label="Выбрать" color="neutral" @click="onFileClick" />
            <input
              ref="fileRef"
              type="file"
              class="hidden"
              accept=".jpg, .jpeg, .png, .gif"
              @change="onFileChange"
            />
          </div>
        </UFormField>
        <USeparator />
        <UFormField
          name="bio"
          label="О себе"
          description="Напишите немного информации о себе"
          class="flex items-start justify-between gap-4 max-sm:flex-col"
          :ui="{ container: 'w-full' }"
        >
          <UTextarea v-model="profile.bio" :rows="5" autoresize class="w-full" />
        </UFormField>
      </UPageCard>
    </UForm>

    <UPageCard
      v-if="user?.has_password"
      title="Пароль"
      description="Перед установкой нового пароля укажите свой текущий пароль."
      variant="subtle"
    >
      <UForm
        ref="formUpdatePassword"
        :state="stateUpdatePassword"
        class="flex max-w-xs flex-col gap-4"
      >
        <UFormField name="password">
          <UInput
            v-model="stateUpdatePassword.password"
            type="password"
            placeholder="Ваш текущий пароль"
            class="w-full"
          />
        </UFormField>

        <UFormField name="new_password">
          <UInput
            v-model="stateUpdatePassword.new_password"
            type="password"
            placeholder="Новый пароль"
            class="w-full"
          />
        </UFormField>

        <UFormField name="new_password_confirmation">
          <UInput
            v-model="stateUpdatePassword.new_password_confirmation"
            type="password"
            placeholder="Подтверждение нового пароля"
            class="w-full"
          />
        </UFormField>

        <UButton
          :loading="isPasswordLoading"
          label="Обновить пароль"
          class="w-fit"
          type="submit"
          @click="updatePassword"
        />
      </UForm>
    </UPageCard>
    <UPageCard
      v-else
      title="Пароль"
      description="Вы можете установить пароль и входить без СМС кода"
      variant="subtle"
    >
      <UForm
        ref="formSetPassword"
        :state="stateSetPassword"
        class="flex max-w-xs flex-col gap-4"
        @submit="setPassword"
      >
        <UFormField name="password">
          <UInput
            v-model="stateSetPassword.password"
            type="password"
            placeholder="Новый пароль"
            class="w-full"
          />
        </UFormField>

        <UFormField name="password_confirmation">
          <UInput
            v-model="stateSetPassword.password_confirmation"
            type="password"
            placeholder="Подтверждение пароля"
            class="w-full"
          />
        </UFormField>

        <UButton
          :loading="isPasswordLoading"
          label="Сохранить пароль"
          class="w-fit"
          type="submit"
        />
      </UForm>
    </UPageCard>
  </div>
</template>

<script setup lang="ts">
import type { User } from "~/types/auth";

const client = useSanctumClient();
const user = useSanctumUser<User>();
const { refreshIdentity } = useSanctumAuth();
const form = useTemplateRef("form");
const fileRef = ref<HTMLInputElement>();
const formSetPassword = useTemplateRef("formSetPassword");
const formUpdatePassword = useTemplateRef("formUpdatePassword");
const stateSetPassword = reactive({
  password: null,
  password_confirmation: null
});
const stateUpdatePassword = reactive({
  password: null,
  new_password: null,
  new_password_confirmation: null
});

const profile = reactive({
  name: user.value?.name,
  email: user.value?.email,
  phone: user.value?.phone,
  theme: user.value?.theme,
  avatar_preview: typeof user.value?.avatar === "object" ? user.value?.avatar?.src : undefined,
  bio: undefined
});

const hasChanges = computed(() => {
  return (
    profile.theme !== user.value?.theme ||
    profile.name !== user.value?.name ||
    profile.bio !== user.value?.bio
  );
});

const formData = new FormData();
const toast = useToast();
const isLoading = ref(false);
const isPasswordLoading = ref(false);

async function onSubmit() {
  isLoading.value = true;

  try {
    for (const [key, value] of Object.entries(profile)) {
      if (value) formData.append(key, value);
    }
    await client("/user", {
      method: "POST",
      body: formData
    });
    await refreshIdentity();
    toast.add({
      id: "profile-update-success",
      title: "Ваш профиль успешно обновлен",
      icon: "i-lucide-check",
      color: "success"
    });
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      form.value?.setErrors(err.bag);
    }
  }

  isLoading.value = false;
}

async function setPassword() {
  isPasswordLoading.value = true;

  try {
    await client("/user/set-password", {
      method: "POST",
      body: stateSetPassword
    });

    await refreshIdentity();

    toast.add({
      id: "profile-update-success",
      title: "Ваш профиль успешно установлен",
      icon: "i-lucide-check",
      color: "success"
    });
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      formSetPassword.value?.setErrors(err.bag);
    }
  }

  isPasswordLoading.value = false;
}
async function updatePassword() {
  isPasswordLoading.value = true;

  try {
    await client("/user/update-password", {
      method: "POST",
      body: stateUpdatePassword
    });

    stateUpdatePassword.password = null;
    stateUpdatePassword.new_password = null;
    stateUpdatePassword.new_password_confirmation = null;

    await refreshIdentity();

    toast.add({
      id: "profile-update-success",
      title: "Ваш профиль успешно обновлен",
      icon: "i-lucide-check",
      color: "success"
    });
  } catch (error) {
    const err = useSanctumError(error);
    if (err.isValidationError) {
      formUpdatePassword.value?.setErrors(err.bag);
    }
  }

  isPasswordLoading.value = false;
}

async function authTelegram() {
  try {
    const { link } = await client("/user/telegram/auth", {
      method: "POST",
      body: stateUpdatePassword
    });
    toast.add({
      id: "profile-telegram",
      title: "Авторизуйтесь в нашем боте по ссылке",
      description: "Перейдите поссылке и запустите бота",
      color: "success",
      duration: 0,
      actions: [
        {
          label: "Перейти в Telegram",
          color: "neutral",
          to: link,
          target: "_blank"
        }
      ]
    });
  } catch (error) {
    console.error(error);
  }
}

function onFileChange(e: Event) {
  const input = e.target as HTMLInputElement;

  if (!input.files?.length) {
    return;
  }

  if (input.files[0]) {
    formData.append("avatar", input.files[0]);
    profile.avatar_preview = URL.createObjectURL(input.files[0]!);
  }
}

function onFileClick() {
  fileRef.value?.click();
}

onMounted(() => {
  refreshIdentity();
});
</script>
