<template>
  <div>
    <UPageCard
      title="Редактирование объявления"
      description="После редактирования объявление отправится на модерацию"
      variant="naked"
      class="mb-4"
    >
      <template #footer>
        <UButton
          size="md"
          icon="i-lucide-arrow-left"
          color="neutral"
          variant="outline"
          to="/profile"
        >
          Назад
        </UButton>
      </template>
    </UPageCard>

    <UForm ref="form" :state="state" class="space-y-4">
      <UAlert
        v-if="state?.moderation == 'not_approved' && state?.published_at"
        color="warning"
        variant="soft"
        icon="i-lucide-circle-alert"
        title="Объявление находится на модерации"
      />

      <div id="step-2" class="space-y-4">
        <CardEditTitle v-model="state.title" />
      </div>

      <div id="step-3">
        <UCard :ui="{ body: 'space-y-4' }">
          <UFormField size="xl" name="year" label="Год выпуска">
            <UInput v-model="state.year" class="w-full" placeholder="2022" />
          </UFormField>
          <UFormField
            v-for="(attrs, name) in attributes"
            :key="name"
            size="xl"
            :name="String(name)"
            :label="attrs.label"
            :required="attrs.required"
          >
            <USelect
              v-model="state.attributes[name]"
              class="w-full"
              :placeholder="attrs.placeholder"
              :items="getValues(name, attrs.values || [])"
            />
          </UFormField>
        </UCard>
      </div>

      <div id="step-4">
        <UCard :ui="{ body: 'space-y-4' }">
          <UFormField required size="xl" name="city" label="Город продажи">
            <USelectMenu v-model="state.city" size="xl" class="w-full" :items="cityStore.cities" />
          </UFormField>

          <UFormField size="xl" name="address" label="Место осмотра:">
            <UButtonGroup v-if="state.address" class="w-full">
              <UInput class="w-full !cursor-default" :value="state.address" disabled />
              <UButton icon="i-lucide-x" square color="error" @click="state.address = null" />
            </UButtonGroup>
            <AddressInput
              v-else
              id="state-address"
              v-model="state.data_address"
              :city="state.city"
            />
          </UFormField>

          <div v-if="state.category?.registration" class="space-y-4">
            <UFormField
              size="xl"
              name="registration_type"
              required
              label="Переоформление:"
              description="Важнейший этап приобретения оружия у физического лица – правильное оформление документов"
            >
              <URadioGroup v-model="state.registration_type" :items="registrationItems" />
            </UFormField>

            <UFormField
              v-if="state.city && state.registration_type"
              required
              size="xl"
              name="registration_address"
              label="Укажите адрес:"
            >
              <UButtonGroup v-if="state.registration_address" class="w-full">
                <UInput
                  class="w-full !cursor-default"
                  :value="state.registration_address"
                  disabled
                />
                <UButton
                  icon="i-lucide-x"
                  square
                  color="error"
                  @click="state.registration_address = null"
                />
              </UButtonGroup>
              <AddressInput
                v-else
                id="state-address"
                v-model="state.data_registration_address"
                :city="state.city"
              />
            </UFormField>
          </div>
          <div v-else class="space-y-4">
            <UFormField required size="xl" name="can_ship">
              <UCheckbox
                v-model="state.can_ship"
                label="Возможна отправка почтой"
                description="Укажите, если вы готовы отправить почтой"
              />
            </UFormField>
          </div>
        </UCard>
      </div>

      <div id="step-5">
        <UCard :ui="{ body: 'space-y-4' }">
          <UFormField
            size="xl"
            required
            name="description"
            label="Подробное описание:"
            help="Опишите свой товар, его состояние, и другую полезную информацию. Укажите способ передачи товара покупателю. Чем больше информации вы укажите, тем более интересным будет объявление с точки зрения покупателя"
          >
            <UTextarea v-model="state.description" class="w-full" :rows="6" autoresize />
          </UFormField>
        </UCard>
      </div>

      <div id="step-6">
        <UCard :ui="{ body: 'space-y-4' }">
          <CardEditPhoto v-model="state.images" :slug="state.slug || ''" :disabled="isLoading" />
        </UCard>
      </div>

      <div id="step-7">
        <UCard :ui="{ body: 'space-y-4' }">
          <UFormField
            size="xl"
            name="price"
            label="Цена"
            help="Введите правильную и полную стоимость Вашего товара. Если Вы оставите поле пустым или напишете 0, будет показана 'Цена договорная'"
            description="Если Вы укажете неверную цену, Ваше объявление и Ваш аккаунт могут быть заблокированы после жалоб пользователей."
          >
            <UInput v-model="state.price" type="number" class="w-full" />
          </UFormField>

          <UFormField required size="xl" name="is_trade">
            <UCheckbox
              v-model="state.is_trade"
              label="Торг уместен"
              description="Укажите, если вы готовы торговаться"
            />
          </UFormField>

          <UFormField required size="xl" name="is_rebate">
            <UCheckbox
              v-model="state.is_rebate"
              label="Обмен возможен"
              description="Укажите, если вы готовы рассмореть обмен"
            />
          </UFormField>
        </UCard>
      </div>

      <div class="flex items-center gap-2">
        <UButton :loading="isLoading" size="xl" type="submit" color="neutral" @click="updatePost">
          Сохранить изменения
        </UButton>
        <UButton :disabled="isLoading" size="xl" to="/profile" variant="link" color="neutral">
          Отменить и выйти
        </UButton>
      </div>
    </UForm>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const client = useSanctumClient();
const cityStore = useCityStore();
const isLoading = ref(false);
const form = useTemplateRef("form");
const { data, refresh } = await useAsyncData(`post:edit:${route.params.slug}`, () =>
  client(`/post/${route.params.slug}`)
);

if (!data.value) {
  throw createError({ statusCode: 404, fatal: true });
}

const { state, registrationItems, getValues, attributes, updatePostAction, uploadImageAction } =
  usePostEdit();

// state = data.value.post
state.attributes = {};
attributes.value = data.value?.attrs;

for (const [key, value] of Object.entries(data.value?.post ?? {})) {
  if (key !== "attributes") state[key] = value;
}

for (const [key, value] of Object.entries(data.value?.attributes ?? {})) {
  state.attributes[key] = String(value);
}

async function updatePost() {
  isLoading.value = true;
  const slug = await updatePostAction();
  await uploadImages();

  if (state.slug !== slug) {
    await navigateTo(`/edit/${slug}`);
  } else {
    await refresh();
  }
  isLoading.value = false;
}

async function uploadImages() {
  isLoading.value = false;

  const error = await uploadImageAction();
  if (error) useSanctumError(error);

  isLoading.value = false;
}
</script>
