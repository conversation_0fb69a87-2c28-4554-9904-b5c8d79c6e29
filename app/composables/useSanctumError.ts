import { FetchError } from "ofetch";
import type { FormError } from "#ui/types";

const VALIDATION_ERROR_CODE = 422;
const SERVER_ERROR_CODE = 500;

function mapLaravelErrorsIntoFormErrors(
  errors: Record<string, string[]> | undefined | null
): FormError[] {
  if (!errors) {
    return [];
  }

  return Object.entries(errors).map(([key, messages]) => ({
    name: key,
    message: messages[0] ?? ""
  }));
}

export const useSanctumError = (error: unknown) => {
  const isFetchError = error instanceof FetchError;
  const isValidationError = isFetchError && error.response?.status === VALIDATION_ERROR_CODE;

  const code = isFetchError ? error.response?.status : SERVER_ERROR_CODE;

  let bag: FormError[] = [];

  if (isValidationError) {
    const responseData = error.response?._data;
    if (responseData?.errors) {
      bag = mapLaravelErrorsIntoFormErrors(responseData.errors);
    } else if (responseData?.message) {
      bag = [];
      useToast().add({
        title: responseData.message,
        color: "error"
      });
    }
  }

  return {
    isValidationError,
    code,
    bag
  };
};
