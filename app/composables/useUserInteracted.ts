import { onMounted, onBeforeUnmount } from "vue";
import { useState } from "#app";

/**
 * Хранит флаг: было ли первое взаимодействие пользователя.
 */
export function useUserInteracted() {
  const hasUserInteracted = useState<boolean>("hasUserInteracted", () => false);

  return { hasUserInteracted };
}

/**
 * Регистрирует один раз слушатели «первого» взаимодействия
 * и дергает переданный handler.
 */
export function useInteractionEvents(handler: () => void) {
  const events = ["mousemove", "scroll", "keydown", "click", "touchstart", "wheel"];

  onMounted(() => {
    if (!import.meta.client) return;

    events.forEach((evt) => window.addEventListener(evt, handler, { once: true }));
  });

  onBeforeUnmount(() => {
    events.forEach((evt) => window.removeEventListener(evt, handler));
  });
}
