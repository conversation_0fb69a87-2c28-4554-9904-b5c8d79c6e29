import { createSharedComposable } from "@vueuse/core";
import * as v from "valibot";
import type { FormError } from "@nuxt/ui";
import * as Sentry from "@sentry/nuxt";
import type { PostAttribute, PostState } from "~/types/post";

const _usePostEdit = () => {
  const cityStore = useCityStore();
  const client = useSanctumClient();

  const emptyState = <PostState>{
    step: 1,
    title: "",
    description: "",
    year: null,
    price: 0,
    is_trade: false,
    is_rebate: false,
    can_ship: false,
    category: undefined,
    contact: "",
    registration_type: "",
    address: null,
    slug: null,
    city: cityStore?.city,
    images: [],
    attributes: {},
    data_address: undefined,
    data_registration_address: undefined,
    registration_address: null,
    uuid: undefined
  };

  const state = reactive<PostState>({ ...{}, ...emptyState });
  const registrationItems = ref([
    {
      label: "В ЛРО",
      description:
        "Подача и оформление документов в лицензионно-разрешительном отделе МВД по месту регистрации",
      value: "OLRR"
    },
    {
      label: "В оружейном магазине",
      description: "Переоформление в специализированном магазине",
      value: "STORE"
    }
  ]);

  const attributes = ref<Record<string, PostAttribute>>({});
  const schemaStepCategory = v.object({
    category: v.object({
      slug: v.string(),
      label: v.string(),
      registration: v.boolean()
    })
  });
  const schemaStepTitle = v.object({
    title: v.pipe(
      v.string(),
      v.trim(),
      v.nonEmpty("Название не может быть пустым"),
      v.minLength(5, "Название должно быть не короче 5 символов"),
      v.maxLength(100, "Название должно быть не длиннее 100 символов")
    )
  });
  const schemaStepDescription = v.object({
    description: v.pipe(
      v.nullable(v.string("Описание не может быть пустым")),
      v.transform((value) => (value === null ? "" : value)),
      v.trim(),
      v.nonEmpty("Описание не может быть пустым"),
      v.minLength(10, "Описание должно быть не короче 10 символов"),
      v.maxLength(1000, "Описание должно быть не длиннее 1000 символов")
    )
  });
  const schemaStepPrice = v.object({
    price: v.pipe(v.number(), v.minValue(0, "Цена не может быть отрицательной")),
    is_trade: v.boolean(),
    is_rebate: v.boolean()
  });

  const validateStepAddress = (state: PostState): FormError[] => {
    const errors = [];
    if (!state.city?.label) errors.push({ name: "city", message: "Пожалуйста, ваш город" });

    if (state.category?.registration) {
      if (!state.registration_type?.length)
        errors.push({
          name: "registration_type",
          message: "Пожалуйста, укажите место для перерегистрации"
        });
      if (!(state.registration_address || state.data_registration_address))
        errors.push({
          name: "registration_address",
          message: "Пожалуйста, укажите адрес переоформления"
        });
    }
    return errors;
  };
  const validateStepImages = (state: PostState): FormError[] => {
    const errors = [];
    if (!state.images?.length)
      errors.push({
        name: "images",
        message: "Пожалуйста, добавьте фотографии"
      });
    return errors;
  };

  const uploadImageAction = async (uuid?: string) => {
    const images = state.images.filter((img) => !img.id);

    if (!state.slug) return null;
    if (!images?.length) return null;

    for (const img of images) {
      if (img.file && !img.id) {
        img.loading = true;
        try {
          const formData = new FormData();
          formData.append("photo", img.file);
          if (uuid) {
            formData.append("uuid", uuid);
          }

          const { id } = await client(`/post/${state.slug}/image`, {
            method: "POST",
            body: formData
          });

          img.id = id;
        } catch (error) {
          Sentry.captureException(error);
          return error;
        }
        img.loading = false;
      }
    }

    return null;
  };

  const validateStepParameters = (state: PostState): FormError[] => {
    const errors: FormError[] = [];

    // Проверяем обязательные атрибуты
    Object.entries(attributes.value).forEach(([name, attr]) => {
      if (attr.required && !state.attributes[name]) {
        errors.push({
          name,
          message: `Поле ${attr.label} должно быть заполнено`
        });
      }
    });

    return errors;
  };

  /**
   * Получает список значений фильтра, учитывая gun_type.
   * @param filter_name Имя фильтра
   * @param values Массив значений фильтра
   * @returns Отфильтрованный массив значений
   */
  const getValues = (filter_name: string, values: Array<{ value: string; label: string }>) => {
    if (filter_name === "caliber" && state?.attributes?.gun_type) {
      return values.filter((item) => item.value.startsWith(state.attributes.gun_type as string));
    }

    return values;
  };

  /**
   * Обновляет существующий пост.
   */
  const updatePostAction = async (is_final?: boolean) => {
    if (!state.slug) return;

    // Собираем payload
    const payload: Record<string, unknown> = {};

    const keys = Object.keys(state) as Array<keyof PostState>;
    for (const key of keys) {
      const value = state[key];
      if (
        key !== "images" &&
        key !== "attributes" &&
        key !== "category" &&
        key !== "city" &&
        key !== "data_registration_address" &&
        key !== "data_address" &&
        value &&
        value !== "null"
      ) {
        payload[key] = value;
      }
    }

    // Атрибуты
    payload.attributes = {} as Record<string, string>;
    for (const [key, value] of Object.entries(state.attributes)) {
      if (value && value !== "null") {
        (payload.attributes as Record<string, string>)[key] = value;
      }
    }

    // Адрес регистрации
    payload.data_registration_address = {} as Record<string, string>;
    if (state.data_registration_address) {
      for (const [key, value] of Object.entries(state.data_registration_address)) {
        if (value && value !== "null") {
          (payload.data_registration_address as Record<string, string>)[key] = value;
        }
      }
    }

    // Адрес
    payload.data_address = {} as Record<string, string>;
    if (state.data_address) {
      for (const [key, value] of Object.entries(state.data_address)) {
        if (value && value !== "null") {
          (payload.data_address as Record<string, string>)[key] = value;
        }
      }
    }

    // Категория и город
    payload.category = state.category?.value || "";
    payload.city = state.city?.value || "";

    if (is_final) {
      payload.is_final = true;
    }

    try {
      const { slug } = await client(`/post/${state.slug}`, {
        method: "POST",
        body: payload
      });

      return slug;
    } catch (error) {
      useSanctumError(error);
      Sentry.captureException(error);

      return null;
    }
  };

  const resetState = () => {
    Object.assign(state, emptyState);
  };

  return {
    state,
    resetState,
    registrationItems,
    attributes,
    schemaStepCategory,
    schemaStepTitle,
    schemaStepDescription,
    schemaStepPrice,
    validateStepAddress,
    validateStepImages,
    validateStepParameters,
    uploadImageAction,
    updatePostAction,
    getValues
  };
};
export const usePostEdit = createSharedComposable(_usePostEdit);
