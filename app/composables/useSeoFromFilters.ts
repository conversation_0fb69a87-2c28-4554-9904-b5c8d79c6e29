// composables/useSeoFromFilters.ts
import { computed } from "vue";
import type { Ref } from "vue";
import type { RouteLocationNormalized } from "vue-router";
import type { PostFilters, PostsResponse } from "~/types/listing";

const truncate = (text: string, maxLength: number = 60): string =>
  text.length > maxLength ? text.slice(0, maxLength - 1).trim() + "…" : text;

const getActiveFiltersText = (
  filters: PostFilters | undefined,
  query: Record<string, string> = {}
): string => {
  if (!filters || !query) return "";

  return Object.entries(query)
    .map(([key, value]) => {
      const filter = filters[key as keyof PostFilters];
      if (!filter) return "";
      const option = filter.values?.find((v) => v.value === value);
      return option?.label;
    })
    .filter(Boolean)
    .join(" ");
};

const getTypeFromParams = (
  types: PostFilters["types"] | undefined,
  typeParam: string | undefined
) => {
  return types?.values?.find((t) => t.value === typeParam);
};

const buildParts = (
  { type, category, city }: { type?: { label: string }; category?: string; city?: string },
  format: "title" | "h1"
): string[] => {
  const parts: string[] = [];

  if (type) {
    parts.push(type.label);
  }

  if (category) {
    parts.push(category);
  }

  if (format === "title") {
    parts.push("объявления о продаже");
  }

  if (city) {
    parts.push(`в ${city}`);
  }

  return parts;
};

const getBaseData = (
  posts: PostsResponse | undefined,
  params: Record<string, string | string[]>
) => {
  const types = posts?.filters?.gun_types ?? posts?.filters?.types;
  return {
    city: posts?.city?.name_pred,
    category: posts?.category?.name,
    type: getTypeFromParams(types, params?.type as string),
    seo: posts?.seo
  };
};

export function useSeoFromFilters(posts: Ref<PostsResponse>, route: RouteLocationNormalized) {
  const params = route.params;
  const page = computed(() => Number(route.query?.page) || 1);

  const baseData = computed(() => getBaseData(posts.value, params));

  const title = computed(() => {
    const { seo, city, category, type } = baseData.value;
    if (seo?.title) return truncate(seo.title, 60);

    const parts = buildParts({ type, category, city }, "title");
    let text = truncate(parts.filter(Boolean).join(" "), 60);
    if (page.value > 1) {
      text += ` – Страница ${page.value}`;
    }

    return text;
  });
  const ogTitle = title.value;

  const h1 = computed(() => {
    const { seo, city, category, type } = baseData.value;
    if (seo?.h1) return truncate(seo.h1);

    const parts = buildParts({ type, category, city }, "h1");
    return truncate(parts.filter(Boolean).join(" "), 120);
  });

  const partsOfDescription = computed(() => {
    const { seo, city, category, type } = baseData.value;
    if (seo?.meta_description) return truncate(seo.meta_description, 160);

    const filtersText = getActiveFiltersText(
      posts.value?.filters,
      route.query as Record<string, string>
    );

    const parts = [
      "Объявления о продаже",
      filtersText,
      type?.label ?? category,
      city ? `в ${city}` : "",
      "от частных лиц. Сайт покупки и продажи оружия."
    ];

    return truncate(parts.filter(Boolean).join(" "), 160);
  });

  const description = computed(() => {
    let stringDescription = partsOfDescription.value;
    if (page.value > 1) {
      stringDescription += ` – Страница ${page.value}`;
    }

    return stringDescription;
  });
  const ogDescription = description.value;
  const pageDescription = partsOfDescription.value;

  return {
    title,
    description,
    ogTitle,
    ogDescription,
    pageDescription,
    h1
  };
}
