import { computed } from "vue";
import type { Post } from "~/types/post";

const truncate = (text: string, maxLength: number = 60): string =>
  text.length > maxLength ? text.slice(0, maxLength - 1).trim() + "…" : text;

const joinParts = (parts: (string | undefined)[], separator: string = " "): string => {
  return parts.filter(Boolean).join(separator);
};

const getPostTitle = (post: Post | undefined): string => {
  if (!post) return "Продажа оружия | GunPost";

  const parts = [
    post.title,
    "купить",
    post.gun_types?.name?.toLowerCase(),
    post.calibers?.name?.toLowerCase(),
    post?.city_name ? `в ${post.city_name}` : ""
  ];

  return truncate(joinParts(parts), 60);
};

const getPostDescription = (post: Post | undefined): string => {
  if (!post)
    return "Объявления о продаже гражданского оружия. Купить бу оружие в России — безопасно и удобно на GunPost.";

  const condition = post.attributes
    ?.find((attr) => attr.type === "conditions")
    ?.name?.toLowerCase();
  const descParts = [
    `${post.title} — ${post.category_name?.toLowerCase() || "оружие"}`,
    condition ? `Состояние: ${condition}` : "",
    post.description_short,
    post.city?.name ? `Регион: ${post.city.name}` : "",
    "Продажа от частных лиц"
  ];

  return truncate(joinParts(descParts, ". "), 160);
};

export function useSeoFromPost(post: Ref<Post | undefined>) {
  const title = computed(() => getPostTitle(post.value));
  const description = computed(() => getPostDescription(post.value));
  const ogImage = computed(() => post.value?.thumbnails?.[0] || "");

  return {
    title,
    description,
    ogImage
  };
}
