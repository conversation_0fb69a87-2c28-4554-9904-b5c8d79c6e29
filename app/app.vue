<template>
  <UApp :locale="ru">
    <NuxtLoadingIndicator />

    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>

    <ClientOnly>
      <ModalsAboutModal />
      <ModalsAuthModal v-if="!isAuthenticated" />
    </ClientOnly>

    <LazyUBanner
      v-if="false"
      id="law-consent"
      color="neutral"
      title="Внимание! Огнестрельное оружие, представленное на сайте, не подлежит свободной продаже в соответствии с законом  Об оружии РФ "
      :actions="[
        {
          label: 'Подробнее',
          color: 'neutral',
          variant: 'outline',
          target: '_blank',
          to: '/zakon'
        }
      ]"
    />
  </UApp>
</template>

<script setup lang="ts">
import { ru } from "@nuxt/ui-pro/locale";
import { useUserInteracted, useInteractionEvents } from "~/composables/useUserInteracted";
import type { User } from "~/types/auth";

const cityStore = useCityStore();
const colorMode = useColorMode();
const { isAuthenticated } = useSanctumAuth();
const { hasUserInteracted } = useUserInteracted();
const user = useSanctumUser<User>();
const toast = useToast();
const client = useSanctumClient();
const consentAdult = useCookie("adult-consent");
const consentCookie = useCookie("cookie-consent");

const acceptCookieBanner = async () => {
  consentCookie.value = "accepted";
  if (!isAuthenticated.value) return false;

  await client("/user/banner", {
    method: "POST",
    body: {
      type: "cookie-consent"
    }
  });
};

const acceptAdultBanner = async () => {
  consentAdult.value = "accepted";
  if (!isAuthenticated.value) return false;

  await client("/user/banner", {
    method: "POST",
    body: {
      type: "adult-consent"
    }
  });
};

const showCookieConsent = () => {
  if (consentCookie.value !== "accepted") {
    toast.add({
      id: "cookie-consent",
      title: "Используя сайт, вы соглашаетесь с политикой cookie",
      icon: "i-lucide-cookie",
      orientation: "horizontal",
      duration: 0,
      close: false,
      actions: [
        {
          label: "Хорошо",
          color: "neutral",
          variant: "outline",
          onClick: () => {
            acceptCookieBanner();
          }
        }
      ]
    });
  }
};

const showAdultConsent = () => {
  if (consentAdult.value !== "accepted") {
    toast.add({
      id: "adult-consent",
      title:
        "Внимание! Данный сайт предназначен для лиц старше 18 лет. Если Вам меньше 18 лет, просим покинуть наш сайт.",
      avatar: {
        src: "/images/adult.png"
      },
      color: "error",
      duration: 0,
      close: false,
      actions: [
        {
          label: "Хорошо",
          color: "neutral",
          size: "sm",
          onClick: () => {
            acceptAdultBanner();
          }
        }
      ]
    });
  }
};

const handleUserInteraction = () => {
  if (!hasUserInteracted.value) {
    hasUserInteracted.value = true;
    showCookieConsent();
    showAdultConsent();
  }
};

onMounted(async () => {
  if (isAuthenticated.value) {
    if (colorMode.preference !== user.value?.theme) {
      colorMode.preference = user.value?.theme ?? "system";
    }
  }
});

await callOnce(async () => {
  await cityStore.fetch();
});

useInteractionEvents(handleUserInteraction);
</script>
