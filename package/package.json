{"name": "@nuxt/ui-pro", "description": "Ship beautiful web applications at the speed of light with Nuxt UI Pro.", "version": "3.3.2", "packageManager": "pnpm@10.14.0", "repository": {"type": "git", "url": "git+https://github.com/nuxt/ui-pro.git"}, "homepage": "https://ui.nuxt.com/pro", "type": "module", "exports": {".": {"types": "./dist/module.d.mts", "style": "./dist/runtime/index.css", "import": "./dist/module.mjs"}, "./runtime/*": "./dist/runtime/*", "./unplugin": {"types": "./dist/unplugin.d.mts", "import": "./dist/unplugin.mjs"}, "./vite": {"types": "./dist/vite.d.mts", "import": "./dist/vite.mjs"}, "./utils": {"types": "./dist/runtime/utils/index.d.ts", "import": "./dist/runtime/utils/index.js"}, "./utils/*": {"types": "./dist/runtime/utils/*.d.ts", "import": "./dist/runtime/utils/*.js"}, "./locale": {"types": "./dist/runtime/locale/index.d.ts", "import": "./dist/runtime/locale/index.js"}}, "typesVersions": {"*": {".": ["./dist/module.d.mts"], "./runtime/*": ["./dist/runtime/*"], "./unplugin": ["./dist/unplugin.d.mts"], "./vite": ["./dist/vite.d.mts"], "./utils": ["./dist/runtime/utils/index.d.ts"], "./utils/*": ["./dist/runtime/utils/*.d.ts"], "./locale": ["./dist/runtime/locale/index.d.ts"]}}, "imports": {"#build/ui-pro/*": "./.nuxt/ui-pro/*.ts", "#build/ui-pro.css": "./.nuxt/ui-pro.css"}, "style": "./dist/runtime/index.css", "main": "./dist/module.mjs", "files": [".nuxt/ui-pro", ".nuxt/ui-pro.css", "dist"], "theme": {"env": "NUXT_UI_PRO_LICENSE", "link": "https://ui.nuxt.com/pro/pricing"}, "keywords": ["nuxt", "ui", "ui-pro", "components", "nuxt-module", "ui-library", "vue"], "scripts": {"build": "nuxt-module-build build", "prepack": "pnpm build", "dev": "nuxt dev playground --ui<PERSON>ev", "dev:build": "nuxt build playground", "dev:vue": "pnpm --filter playground-vue dev -- --uiDev", "dev:vue:build": "pnpm --filter playground-vue build", "dev:prepare": "nuxt-module-build build --stub && nuxt-module-build prepare && nuxt prepare playground && nuxt prepare docs && vite build playground-vue", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "vue-tsc --noEmit && nuxt typecheck playground && nuxt typecheck docs && cd playground-vue && vue-tsc --noEmit", "test": "vitest", "test:vue": "vitest -c vitest.vue.config.ts", "release": "release-it"}, "dependencies": {"@ai-sdk/vue": "^1.2.12", "@nuxt/kit": "^4.0.3", "@nuxt/schema": "^4.0.3", "@nuxt/ui": "^3.3.2", "@standard-schema/spec": "^1.0.0", "@vueuse/core": "^13.6.0", "consola": "^3.4.2", "defu": "^6.1.4", "dotenv": "^16.6.1", "git-url-parse": "^16.1.0", "motion-v": "^1.7.0", "ofetch": "^1.4.1", "ohash": "^2.0.11", "pathe": "^2.0.3", "pkg-types": "^2.2.0", "scule": "^1.3.0", "tinyglobby": "^0.2.14", "unplugin": "^2.3.5", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0"}, "devDependencies": {"@nuxt/content": "^3.6.3", "@nuxt/eslint-config": "^1.8.0", "@nuxt/module-builder": "^1.0.1", "@nuxt/test-utils": "^3.19.2", "@release-it/conventional-changelog": "^10.0.1", "@vue/test-utils": "^2.4.6", "better-sqlite3": "^12.2.0", "eslint": "^9.33.0", "happy-dom": "^18.0.1", "nuxt": "^4.0.3", "release-it": "^19.0.4", "vitest": "^3.2.4", "vitest-environment-nuxt": "^1.0.1", "vue-tsc": "^3.0.5"}, "peerDependencies": {"joi": "^17.13.0", "superstruct": "^2.0.0", "typescript": "^5.6.3", "valibot": "^1.0.0", "yup": "^1.6.0", "zod": "^3.24.0 || ^4.0.0"}, "peerDependenciesMeta": {"joi": {"optional": true}, "valibot": {"optional": true}, "superstruct": {"optional": true}, "yup": {"optional": true}, "zod": {"optional": true}}, "resolutions": {"@nuxt/ui-pro": "workspace:*", "@nuxt/module-builder": "1.0.1", "debug": "4.3.7", "rollup": "4.34.9", "unimport": "4.1.1", "unplugin": "^2.3.5", "typescript": "5.8.3"}, "pnpm": {"onlyBuiltDependencies": ["better-sqlite3"], "ignoredBuiltDependencies": ["esbuild"]}}